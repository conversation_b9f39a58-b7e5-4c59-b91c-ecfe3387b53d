<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Contrôleur pour l'intégration du chatbot COBIT 2019
 * Fait le pont entre le frontend Laravel et l'API FastAPI du chatbot
 */
class ChatbotController extends Controller
{
    /**
     * URL de base de l'API FastAPI du chatbot
     */
    private const CHATBOT_API_URL = 'http://localhost:8001';
    
    /**
     * Timeout pour les requêtes vers l'API chatbot (en secondes)
     */
    private const REQUEST_TIMEOUT = 120;

    /**
     * Vérifier l'état de santé du chatbot (version intégrée)
     *
     * @return JsonResponse
     */
    public function health(): JsonResponse
    {
        // Chatbot intégré toujours disponible
        return response()->json([
            'status' => 'success',
            'chatbot_available' => true,
            'data' => [
                'version' => '1.0.0',
                'type' => 'integrated',
                'capabilities' => ['cobit_2019', 'governance', 'design_factors']
            ]
        ]);
    }

    /**
     * Envoyer une question au chatbot et retourner la réponse (version intégrée)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function query(Request $request): JsonResponse
    {
        // Validation de la requête
        $request->validate([
            'question' => 'required|string|min:3|max:1000'
        ]);

        $question = trim($request->input('question'));

        Log::info('Question reçue par le chatbot intégré: ' . $question);

        // Générer une réponse basée sur COBIT 2019
        $answer = $this->generateCobitResponse($question);

        return response()->json([
            'status' => 'success',
            'question' => $question,
            'answer' => $answer,
            'timestamp' => now()->toISOString(),
            'source' => 'integrated_chatbot'
        ]);
    }

    /**
     * Générer une réponse COBIT 2019 basée sur la question
     */
    private function generateCobitResponse(string $question): string
    {
        $questionLower = strtolower($question);

        // Réponses sur les Design Factors
        if (preg_match('/design factor|df(\d+)|facteur/i', $questionLower)) {
            return $this->getDesignFactorResponse($questionLower);
        }

        // Réponses sur les objectifs de gouvernance
        if (preg_match('/edm|gouvernance|objectif.*gouvernance/i', $questionLower)) {
            return $this->getGovernanceResponse($questionLower);
        }

        // Réponses sur les domaines de gestion
        if (preg_match('/apo|bai|dss|mea|gestion/i', $questionLower)) {
            return $this->getManagementResponse($questionLower);
        }

        // Réponses générales sur COBIT
        if (preg_match('/cobit|qu\'est-ce|définition|principe/i', $questionLower)) {
            return $this->getGeneralCobitResponse($questionLower);
        }

        // Réponses sur l'évaluation
        if (preg_match('/évaluation|comment.*évaluer|maturité/i', $questionLower)) {
            return $this->getEvaluationResponse($questionLower);
        }

        // Réponse par défaut
        return "Je suis votre assistant COBIT 2019. Je peux vous aider avec :\n\n" .
               "🎯 **Les 10 Design Factors** (DF1 à DF10)\n" .
               "📊 **L'évaluation de maturité**\n" .
               "🏛️ **Les objectifs de gouvernance** (EDM)\n" .
               "⚙️ **Les domaines de gestion** (APO, BAI, DSS, MEA)\n" .
               "📋 **Le processus d'évaluation**\n\n" .
               "Posez-moi une question plus spécifique sur COBIT 2019 !";
    }

    /**
     * Réponses sur les Design Factors
     */
    private function getDesignFactorResponse(string $question): string
    {
        if (preg_match('/df1|design factor 1|stratégie/i', $question)) {
            return "**DF1 - Enterprise Strategy** 🎯\n\n" .
                   "Ce Design Factor évalue la stratégie d'entreprise et son alignement avec l'IT :\n\n" .
                   "• **Objectifs stratégiques** clairs et mesurables\n" .
                   "• **Vision IT** alignée sur la stratégie métier\n" .
                   "• **Planification** à long terme\n" .
                   "• **Gouvernance** stratégique\n\n" .
                   "💡 **Conseil** : Assurez-vous que votre stratégie IT supporte directement les objectifs business !";
        }

        if (preg_match('/df2|design factor 2|objectifs/i', $question)) {
            return "**DF2 - Enterprise Goals** 📊\n\n" .
                   "Ce Design Factor concerne les objectifs et métriques d'entreprise :\n\n" .
                   "• **KPI** et indicateurs de performance\n" .
                   "• **Mesure** de la valeur créée par l'IT\n" .
                   "• **Alignement** objectifs IT/Business\n" .
                   "• **Reporting** et tableaux de bord\n\n" .
                   "💡 **Conseil** : Définissez des métriques SMART pour mesurer le succès !";
        }

        return "Les **10 Design Factors** de COBIT 2019 sont :\n\n" .
               "🎯 **DF1** - Enterprise Strategy\n" .
               "📊 **DF2** - Enterprise Goals\n" .
               "⚠️ **DF3** - Risk Profile\n" .
               "🔧 **DF4** - I&T-Related Issues\n" .
               "🛡️ **DF5** - Threat Landscape\n" .
               "📋 **DF6** - Compliance Requirements\n" .
               "🏛️ **DF7** - Role of IT\n" .
               "🤝 **DF8** - Sourcing Model\n" .
               "⚙️ **DF9** - IT Implementation Methods\n" .
               "🏢 **DF10** - Enterprise Size\n\n" .
               "Demandez-moi des détails sur un Design Factor spécifique !";
    }

    /**
     * Réponses sur la gouvernance
     */
    private function getGovernanceResponse(string $question): string
    {
        return "**Gouvernance IT selon COBIT 2019** 🏛️\n\n" .
               "La gouvernance comprend **5 objectifs principaux** :\n\n" .
               "📋 **EDM01** - Assurer la définition et le maintien du cadre de gouvernance\n" .
               "💰 **EDM02** - Assurer la livraison des bénéfices\n" .
               "⚠️ **EDM03** - Assurer l'optimisation des risques\n" .
               "💎 **EDM04** - Assurer l'optimisation des ressources\n" .
               "👥 **EDM05** - Assurer la transparence envers les parties prenantes\n\n" .
               "💡 **Principe clé** : La gouvernance **dirige** et **supervise**, elle ne gère pas directement !";
    }

    /**
     * Obtenir l'historique des conversations (pour une future implémentation)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function history(Request $request): JsonResponse
    {
        // Pour l'instant, retourner un tableau vide
        // Dans une future version, on pourrait stocker l'historique en base
        return response()->json([
            'status' => 'success',
            'history' => []
        ]);
    }

    /**
     * Réponses sur les domaines de gestion
     */
    private function getManagementResponse(string $question): string
    {
        return "**Domaines de Gestion COBIT 2019** ⚙️\n\n" .
               "Les **4 domaines de gestion** sont :\n\n" .
               "📋 **APO** (Align, Plan, Organize) - 14 objectifs\n" .
               "🔨 **BAI** (Build, Acquire, Implement) - 11 objectifs\n" .
               "🚀 **DSS** (Deliver, Service, Support) - 6 objectifs\n" .
               "📊 **MEA** (Monitor, Evaluate, Assess) - 4 objectifs\n\n" .
               "💡 **Total** : 35 objectifs de gestion pour couvrir tout le cycle de vie IT !";
    }

    /**
     * Réponses générales sur COBIT
     */
    private function getGeneralCobitResponse(string $question): string
    {
        return "**COBIT 2019 - Référentiel de Gouvernance IT** 🏛️\n\n" .
               "COBIT (Control Objectives for Information and Related Technologies) est :\n\n" .
               "🎯 **Un framework** de gouvernance et gestion IT\n" .
               "📊 **40 objectifs** (5 gouvernance + 35 gestion)\n" .
               "🔧 **10 Design Factors** pour personnaliser l'implémentation\n" .
               "📈 **6 niveaux de maturité** (0 à 5)\n\n" .
               "💡 **Objectif** : Créer de la valeur optimale à partir de l'IT tout en gérant les risques !";
    }

    /**
     * Réponses sur l'évaluation
     */
    private function getEvaluationResponse(string $question): string
    {
        return "**Évaluation COBIT 2019** 📊\n\n" .
               "Le processus d'évaluation comprend :\n\n" .
               "1️⃣ **Analyse des 10 Design Factors**\n" .
               "2️⃣ **Évaluation des 40 objectifs** (échelle 1-5)\n" .
               "3️⃣ **Calcul du niveau de maturité**\n" .
               "4️⃣ **Génération de recommandations**\n\n" .
               "🤖 **Astuce** : Utilisez l'Agent IA pour analyser vos documents et pré-remplir l'évaluation !\n\n" .
               "📈 **Niveaux de maturité** :\n" .
               "• **0** - Inexistant\n" .
               "• **1** - Initial\n" .
               "• **2** - Géré\n" .
               "• **3** - Défini\n" .
               "• **4** - Quantitativement géré\n" .
               "• **5** - Optimisé";
    }

    /**
     * Obtenir des suggestions de questions prédéfinies
     *
     * @return JsonResponse
     */
    public function suggestions(): JsonResponse
    {
        $suggestions = [
            [
                'category' => 'Design Factors',
                'questions' => [
                    'Expliquez le Design Factor 1',
                    'Quels sont les 10 Design Factors ?',
                    'Comment utiliser les Design Factors ?'
                ]
            ],
            [
                'category' => 'Évaluation',
                'questions' => [
                    'Comment évaluer ma maturité COBIT ?',
                    'Quels sont les niveaux de maturité ?',
                    'Comment utiliser l\'Agent IA ?'
                ]
            ],
            [
                'category' => 'Gouvernance',
                'questions' => [
                    'Quels sont les objectifs EDM ?',
                    'Différence gouvernance vs gestion ?',
                    'Comment implémenter la gouvernance ?'
                ]
            ],
            [
                'category' => 'Gestion',
                'questions' => [
                    'Expliquez les domaines APO, BAI, DSS, MEA',
                    'Combien d\'objectifs de gestion ?',
                    'Comment prioriser les objectifs ?'
                ]
            ]
        ];

        return response()->json([
            'status' => 'success',
            'suggestions' => $suggestions
        ]);
    }

    /**
     * Obtenir des statistiques d'utilisation du chatbot
     * 
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        // Pour l'instant, retourner des statistiques fictives
        // Dans une future version, on pourrait les calculer depuis la base de données
        return response()->json([
            'status' => 'success',
            'stats' => [
                'total_questions' => 0,
                'avg_response_time' => '0s',
                'most_asked_topics' => [
                    'Introduction COBIT' => 0,
                    'Objectifs EDM' => 0,
                    'Domaines de gestion' => 0
                ]
            ]
        ]);
    }
}
