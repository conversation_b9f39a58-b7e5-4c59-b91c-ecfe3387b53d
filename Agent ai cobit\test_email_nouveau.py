#!/usr/bin/env python3
"""
Test rapide avec les nouvelles configurations email
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime

def tester_nouveau_email():
    """Test avec les nouvelles configurations."""
    print("🧪 TEST AVEC NOUVELLES CONFIGURATIONS EMAIL")
    print("=" * 50)
    
    # Nouvelles configurations
    expediteur = '<EMAIL>'
    mot_de_passe = 'pyps knxd obnt tisg'
    destinataires = ['<EMAIL>', '<EMAIL>']
    
    print(f"📧 Expéditeur: {expediteur}")
    print(f"📧 Destinataires: {', '.join(destinataires)}")
    print(f"🔑 Mot de passe: {mot_de_passe[:4]}****")
    
    try:
        # Créer le message
        msg = MIMEMultipart()
        msg['From'] = expediteur
        msg['To'] = ', '.join(destinataires)
        msg['Subject'] = f'🧪 Test Agent IA - Nouvelles Config - {datetime.now().strftime("%d/%m/%Y %H:%M")}'
        
        corps = f"""
Bonjour,

Test de l'Agent IA d'Audit Gouvernance IT avec les nouvelles configurations email.

📅 Date: {datetime.now().strftime("%d/%m/%Y à %H:%M")}
📧 Expéditeur: {expediteur}
🎯 Destinataires: {', '.join(destinataires)}
✅ Statut: Configuration mise à jour

Si vous recevez cet email, l'envoi automatique fonctionne parfaitement !

---
🤖 Agent IA d'Audit Gouvernance IT
Test de configuration email réussie
        """
        
        msg.attach(MIMEText(corps, 'plain', 'utf-8'))
        
        # Test SMTP_SSL (port 465)
        print("\n🔐 Test SMTP_SSL (port 465)...")
        try:
            with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                server.login(expediteur, mot_de_passe)
                server.send_message(msg)
                print("✅ SUCCÈS ! Email envoyé via SMTP_SSL")
                return True
        except Exception as e:
            print(f"❌ Échec SSL: {e}")
        
        # Test SMTP avec STARTTLS (port 587)
        print("\n🔓 Test SMTP STARTTLS (port 587)...")
        try:
            with smtplib.SMTP('smtp.gmail.com', 587) as server:
                server.starttls()
                server.ehlo()
                server.login(expediteur, mot_de_passe)
                server.send_message(msg)
                print("✅ SUCCÈS ! Email envoyé via STARTTLS")
                return True
        except Exception as e:
            print(f"❌ Échec STARTTLS: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False

if __name__ == "__main__":
    success = tester_nouveau_email()
    
    if success:
        print("\n🎉 EMAIL ENVOYÉ AVEC SUCCÈS !")
        print("✅ Les nouvelles configurations fonctionnent")
        print("✅ L'Agent IA peut maintenant envoyer des emails automatiquement")
        print("\n📋 PROCHAINES ÉTAPES:")
        print("1. Testez l'upload d'un fichier sur http://localhost:5000")
        print("2. Vérifiez la réception des emails avec PDF")
    else:
        print("\n❌ PROBLÈME PERSISTANT")
        print("Vérifiez les identifiants email")
