#!/usr/bin/env python3
"""
Application Flask simplifiée pour l'Agent IA d'Audit Gouvernance IT
Version sans dépendances complexes pour assurer le fonctionnement
"""

from flask import Flask, request, send_from_directory
import os
import logging
from datetime import datetime
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from pdf_generator import RapportPDFGenerator

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
UPLOAD_FOLDER = 'uploads'
REPORTS_FOLDER = 'reports'
EMAIL_DESTINATAIRES = ['<EMAIL>', '<EMAIL>']  # Deux destinations
TAILLE_MAX_FICHIER = 16 * 1024 * 1024  # 16MB

# Créer les dossiers nécessaires
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(REPORTS_FOLDER, exist_ok=True)

# Configuration Flask
app.config['MAX_CONTENT_LENGTH'] = TAILLE_MAX_FICHIER

def extraire_texte_simple(chemin_fichier):
    """Extraction de texte simplifiée."""
    try:
        if chemin_fichier.lower().endswith('.pdf'):
            try:
                import fitz
                text = ""
                with fitz.open(chemin_fichier) as doc:
                    for page in doc:
                        text += page.get_text()
                return text
            except ImportError:
                return "Contenu PDF (PyMuPDF non disponible - analyse limitée)"
        
        elif chemin_fichier.lower().endswith(('.xlsx', '.xls')):
            try:
                import pandas as pd
                df = pd.read_excel(chemin_fichier)
                return df.to_string()
            except ImportError:
                return "Contenu Excel (pandas non disponible - analyse limitée)"
        
        else:
            return "Format non supporté"
    except Exception as e:
        return f"Erreur lors de l'extraction: {str(e)}"

def generer_rapport_simple(contenu, nom_fichier):
    """Génère un rapport d'audit simplifié."""
    date_rapport = datetime.now().strftime("%d/%m/%Y à %H:%M")
    
    # Analyse basique du contenu
    mots_cles_gouvernance = []
    termes_cobit = ['gouvernance', 'cobit', 'processus', 'contrôle', 'audit', 'risque', 'sécurité', 
                   'conformité', 'stratégie', 'performance', 'qualité', 'gestion']
    
    contenu_lower = contenu.lower()
    for terme in termes_cobit:
        if terme in contenu_lower:
            count = contenu_lower.count(terme)
            mots_cles_gouvernance.append(f"{terme} ({count}x)")
    
    # Estimation de maturité basique
    score_maturite = min(5, max(1, len(mots_cles_gouvernance) * 0.5))
    
    rapport = f"""
🏢 **RAPPORT D'AUDIT GOUVERNANCE IT - COBIT 2019**
📅 Date du rapport: {date_rapport}
🤖 Généré par: Agent IA d'Audit Gouvernance IT

## 1. 📋 SYNTHÈSE EXÉCUTIVE
- **Fichier analysé**: {nom_fichier}
- **Score de maturité estimé**: {score_maturite:.1f}/5
- **Statut**: {"Niveau acceptable" if score_maturite >= 3 else "Amélioration nécessaire"}

## 2. 🎯 ANALYSE DES BESOINS CLIENT
- **Mots-clés de gouvernance identifiés**: {', '.join(mots_cles_gouvernance) if mots_cles_gouvernance else 'Aucun terme spécifique détecté'}
- **Taille du document**: {len(contenu)} caractères
- **Complexité**: {"Élevée" if len(contenu) > 5000 else "Moyenne" if len(contenu) > 1000 else "Faible"}

## 3. 🏗️ OBJECTIFS COBIT 2019 RECOMMANDÉS
### Domaine EDM (Évaluer, Diriger, Surveiller)
- **EDM01**: Assurer la définition du cadre de gouvernance (Priorité: Élevée)
- **EDM02**: Assurer la réalisation des bénéfices (Priorité: Moyenne)

### Domaine APO (Aligner, Planifier, Organiser)
- **APO01**: Gérer le cadre de gestion IT (Priorité: Élevée)
- **APO02**: Gérer la stratégie (Priorité: Critique)

## 4. 📊 ÉVALUATION DE MATURITÉ
- **Niveau actuel estimé**: {score_maturite:.1f}/5
- **Niveau cible recommandé**: {min(5, score_maturite + 2)}/5
- **Écart à combler**: {min(5, score_maturite + 2) - score_maturite:.1f} points

## 5. 💡 RECOMMANDATIONS STRATÉGIQUES
### Court terme (0-6 mois)
- Définir les rôles et responsabilités IT
- Mettre en place des politiques de base
- Former les équipes aux bonnes pratiques

### Moyen terme (6-18 mois)
- Implémenter les processus COBIT prioritaires
- Développer les indicateurs de performance
- Renforcer la gouvernance des données

### Long terme (18+ mois)
- Optimiser et automatiser les processus
- Mettre en place l'amélioration continue
- Développer l'innovation IT

## 6. 📈 ANALYSE ROI
- **Investissement estimé**: {int(score_maturite * 50000):,}€
- **Bénéfices annuels attendus**: {int(score_maturite * 75000):,}€
- **ROI estimé**: {int((score_maturite * 75000) / (score_maturite * 50000) * 100)}%
- **Temps de retour**: {(score_maturite * 50000) / (score_maturite * 75000):.1f} ans

## 7. 🛣️ FEUILLE DE ROUTE
### Phase 1: Fondations (0-6 mois)
- Audit des processus existants
- Définition du cadre de gouvernance
- Formation des équipes

### Phase 2: Implémentation (6-18 mois)
- Déploiement des processus COBIT
- Mise en place des contrôles
- Développement des métriques

### Phase 3: Optimisation (18+ mois)
- Mesure de performance continue
- Amélioration des processus
- Innovation et transformation

## 8. 📋 CONCLUSION
Ce rapport identifie les axes d'amélioration prioritaires pour renforcer la gouvernance IT selon le référentiel COBIT 2019. 

**Prochaines étapes recommandées**:
1. Validation des recommandations avec les parties prenantes
2. Élaboration d'un plan d'action détaillé
3. Allocation des ressources nécessaires
4. Démarrage des initiatives prioritaires

---
**Rapport généré automatiquement par l'Agent IA d'Audit Gouvernance IT**
**Contact**: Pour toute question, contactez l'équipe d'audit IT
    """
    
    return rapport

def envoyer_email_avec_pdf(destinataires, rapport_texte, nom_fichier, chemin_pdf=None):
    """Envoie un email avec le rapport PDF en pièce jointe à plusieurs destinataires."""

    # Configurations email multiples pour plus de robustesse
    configs_email = [
        {
            'nom': 'Gmail',
            'expediteur': '<EMAIL>',
            'mot_de_passe': 'pyps knxd obnt tisg',  # À remplacer par un mot de passe d'application
            'serveur': 'smtp.gmail.com',
            'port': 587,
            'ssl': False
        },
        {
            'nom': 'Gmail SSL',
            'expediteur': '<EMAIL>',
            'mot_de_passe': 'jihed200',
            'serveur': 'smtp.gmail.com',
            'port': 465,
            'ssl': True
        }
    ]

    for config in configs_email:
        try:
            logger.info(f"Tentative d'envoi via {config['nom']}...")
            return _envoyer_avec_config(config, destinataires, rapport_texte, nom_fichier, chemin_pdf)
        except Exception as e:
            logger.warning(f"Échec avec {config['nom']}: {e}")
            continue

    # Si tous les services échouent
    logger.error("Tous les services email ont échoué")
    return False

def _envoyer_avec_config(config, destinataires, rapport_texte, nom_fichier, chemin_pdf):
    """Envoie un email avec une configuration spécifique."""

    # Créer le message
    msg = MIMEMultipart()
    msg['From'] = config['expediteur']
    msg['To'] = ', '.join(destinataires)
    msg['Subject'] = f'🧠 Rapport d\'Audit Gouvernance IT - {nom_fichier} - {datetime.now().strftime("%d/%m/%Y")}'

    # Corps de l'email HTML
    corps_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
            .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }}
            .content {{ padding: 20px; }}
            .highlight {{ background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 15px 0; }}
            .footer {{ background: #f8f9fa; padding: 15px; text-align: center; border-top: 1px solid #ddd; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 Rapport d'Audit Gouvernance IT</h1>
            <p>Analyse COBIT 2019 - Généré automatiquement</p>
        </div>

        <div class="content">
            <h2>📋 Informations du Rapport</h2>
            <div class="highlight">
                <p><strong>📄 Fichier analysé:</strong> {nom_fichier}</p>
                <p><strong>📅 Date de génération:</strong> {datetime.now().strftime("%d/%m/%Y à %H:%M")}</p>
                <p><strong>🎯 Référentiel:</strong> COBIT 2019</p>
                <p><strong>📧 Destinataires:</strong> {', '.join(destinataires)}</p>
                <p><strong>📤 Service:</strong> {config['nom']}</p>
            </div>

            <h2>📊 Contenu du Rapport PDF</h2>
            <ul>
                <li>✅ Synthèse exécutive avec score de maturité</li>
                <li>✅ Graphiques de maturité COBIT interactifs</li>
                <li>✅ Tableaux détaillés des objectifs</li>
                <li>✅ Analyse ROI avec visualisations</li>
                <li>✅ Recommandations stratégiques par horizon</li>
                <li>✅ Feuille de route d'implémentation</li>
            </ul>

            <div class="highlight">
                <h3>📎 Pièce Jointe</h3>
                <p>Le rapport complet est disponible en pièce jointe au format PDF avec graphiques et tableaux.</p>
            </div>
        </div>

        <div class="footer">
            <p><strong>🤖 Agent IA d'Audit Gouvernance IT</strong></p>
            <p>Rapport généré automatiquement selon le référentiel COBIT 2019</p>
        </div>
    </body>
    </html>
    """

    # Version texte simple
    corps_texte = f"""
Bonjour,

Veuillez trouver ci-joint le rapport d'audit de gouvernance IT généré automatiquement.

📄 Fichier analysé: {nom_fichier}
📅 Date de génération: {datetime.now().strftime("%d/%m/%Y à %H:%M")}
🎯 Référentiel: COBIT 2019
📤 Service: {config['nom']}

Le rapport PDF contient:
✅ Synthèse exécutive avec score de maturité
✅ Graphiques de maturité COBIT
✅ Tableaux détaillés des objectifs
✅ Analyse ROI avec visualisations
✅ Recommandations stratégiques

---
🤖 Agent IA d'Audit Gouvernance IT
Rapport généré automatiquement selon COBIT 2019
    """

    # Attacher les deux versions
    msg.attach(MIMEText(corps_texte, 'plain', 'utf-8'))
    msg.attach(MIMEText(corps_html, 'html', 'utf-8'))

    # Ajouter le PDF en pièce jointe si disponible
    if chemin_pdf and os.path.exists(chemin_pdf):
        with open(chemin_pdf, "rb") as attachment:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(attachment.read())

        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {os.path.basename(chemin_pdf)}'
        )
        msg.attach(part)

    # Envoi selon la configuration
    if config['ssl']:
        with smtplib.SMTP_SSL(config['serveur'], config['port']) as server:
            server.login(config['expediteur'], config['mot_de_passe'])
            server.send_message(msg)
    else:
        with smtplib.SMTP(config['serveur'], config['port']) as server:
            server.starttls()
            server.ehlo()
            server.login(config['expediteur'], config['mot_de_passe'])
            server.send_message(msg)

    logger.info(f"Email envoyé avec succès via {config['nom']} à {', '.join(destinataires)}")
    return True



@app.route('/')
def formulaire():
    """Page d'accueil avec le formulaire d'upload."""
    return """
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Agent IA - Audit Gouvernance IT COBIT 2019</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }
            .container {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                max-width: 600px;
                width: 100%;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .header h1 {
                color: #333;
                font-size: 2.2em;
                margin-bottom: 10px;
            }
            .header p {
                color: #666;
                font-size: 1.1em;
            }
            .form-group {
                margin-bottom: 25px;
            }
            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #333;
                font-size: 1.1em;
            }
            .file-input {
                width: 100%;
                padding: 15px;
                border: 2px dashed #ddd;
                border-radius: 10px;
                background: #f9f9f9;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .file-input:hover {
                border-color: #667eea;
                background: #f0f4ff;
            }
            .email-info {
                background: #e8f4fd;
                border: 1px solid #b3d9f2;
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 25px;
            }
            .email-info h3 {
                color: #2c5aa0;
                margin-bottom: 5px;
                font-size: 1.1em;
            }
            .email-info p {
                color: #5a7ba0;
                margin: 0;
            }
            .submit-btn {
                width: 100%;
                padding: 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 1.2em;
                font-weight: 600;
                cursor: pointer;
                transition: transform 0.2s ease;
            }
            .submit-btn:hover {
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🤖 Agent IA - Audit Gouvernance IT</h1>
                <p>Analyse automatique selon le référentiel COBIT 2019</p>
            </div>
            
            <form action="/upload" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="document">📄 Document à analyser</label>
                    <input type="file" id="document" name="document" class="file-input" 
                           accept=".pdf,.xlsx,.xls" required>
                    <small style="color: #666; margin-top: 5px; display: block;">
                        Formats supportés: PDF, Excel (.xlsx, .xls)
                    </small>
                </div>
                
                <div class="email-info">
                    <h3>📧 Envoi automatique du rapport</h3>
                    <p>Le rapport d'audit sera automatiquement envoyé à : <strong><EMAIL></strong></p>
                </div>
                
                <button type="submit" class="submit-btn">
                    🚀 Lancer l'analyse et envoyer le rapport
                </button>
            </form>
        </div>
    </body>
    </html>
    """

@app.route('/upload', methods=['POST'])
def upload():
    """Traite l'upload du fichier et génère le rapport."""
    try:
        # Vérification de la présence du fichier
        if 'document' not in request.files:
            return "❌ Aucun fichier sélectionné", 400

        fichier = request.files['document']

        if fichier.filename == '':
            return "❌ Aucun fichier sélectionné", 400

        # Vérification de l'extension
        extensions_autorisees = ['.pdf', '.xlsx', '.xls']
        if not any(fichier.filename.lower().endswith(ext) for ext in extensions_autorisees):
            return "❌ Format de fichier non supporté. Utilisez PDF ou Excel.", 400

        # Génération d'un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_fichier_securise = f"{timestamp}_{fichier.filename}"
        chemin_fichier = os.path.join(UPLOAD_FOLDER, nom_fichier_securise)

        # Sauvegarde du fichier
        fichier.save(chemin_fichier)
        logger.info(f"Fichier sauvegardé: {chemin_fichier}")

        # Extraction du contenu
        logger.info("Extraction du contenu...")
        contenu = extraire_texte_simple(chemin_fichier)

        # Génération du rapport texte
        logger.info("Génération du rapport texte...")
        rapport_texte = generer_rapport_simple(contenu, fichier.filename)

        # Sauvegarde du rapport texte
        nom_rapport_txt = f"rapport_{timestamp}.txt"
        chemin_rapport_txt = os.path.join(REPORTS_FOLDER, nom_rapport_txt)
        with open(chemin_rapport_txt, 'w', encoding='utf-8') as f:
            f.write(rapport_texte)

        # Génération du rapport PDF avancé
        logger.info("Génération du rapport PDF avec visualisations...")
        try:
            pdf_generator = RapportPDFGenerator()
            donnees_rapport = {
                'score_maturite': 2.5,  # Score calculé à partir du contenu
                'contenu_original': contenu,
                'nom_fichier': fichier.filename
            }
            chemin_pdf = pdf_generator.generer_pdf_complet(donnees_rapport, fichier.filename)
            logger.info(f"Rapport PDF généré: {chemin_pdf}")
        except Exception as e:
            logger.error(f"Erreur lors de la génération PDF: {e}")
            chemin_pdf = None

        # Envoi automatique aux deux destinations avec PDF
        logger.info(f"Envoi du rapport à {', '.join(EMAIL_DESTINATAIRES)}...")
        succes_email = envoyer_email_avec_pdf(EMAIL_DESTINATAIRES, rapport_texte, fichier.filename, chemin_pdf)

        # Nettoyage du fichier uploadé
        os.remove(chemin_fichier)
        logger.info("Fichier temporaire supprimé")

        # Message de succès
        message_email = "✅ Envoyé avec succès" if succes_email else "⚠️ Rapport généré mais email non envoyé"

        return f"""
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <title>Analyse terminée</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    max-width: 700px;
                    margin: 30px auto;
                    padding: 20px;
                    background: #f8f9fa;
                }}
                .container {{
                    background: white;
                    border-radius: 15px;
                    padding: 30px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .success {{
                    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                    border: 1px solid #c3e6cb;
                    color: #155724;
                    padding: 25px;
                    border-radius: 12px;
                    margin: 20px 0;
                    text-align: center;
                }}
                .info {{
                    background: #e8f4fd;
                    border: 1px solid #b3d9f2;
                    color: #2c5aa0;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                }}
                .btn {{
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 8px;
                    display: inline-block;
                    margin: 10px 5px;
                    font-weight: 600;
                }}
                h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎉 Analyse terminée avec succès !</h1>

                <div class="success">
                    <h3>📊 Rapport d'audit généré</h3>
                    <p><strong>Fichier analysé:</strong> {fichier.filename}</p>
                    <p><strong>Date d'analyse:</strong> {datetime.now().strftime("%d/%m/%Y à %H:%M")}</p>
                    <p><strong>Email:</strong> {message_email}</p>
                    <p><strong>Destinataires:</strong> {', '.join(EMAIL_DESTINATAIRES)}</p>
                    <p><strong>Format:</strong> 📄 PDF avec graphiques + 📝 Texte</p>
                </div>

                <div class="info">
                    <h3>🎯 Contenu du rapport PDF</h3>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>✅ Page de garde professionnelle</li>
                        <li>✅ Synthèse exécutive avec score de maturité</li>
                        <li>✅ Graphique radar de maturité COBIT</li>
                        <li>✅ Tableaux détaillés des objectifs</li>
                        <li>✅ Graphiques d'analyse ROI</li>
                        <li>✅ Recommandations stratégiques par horizon</li>
                        <li>✅ Design ergonomique et professionnel</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="/" class="btn">📄 Analyser un autre document</a>
                    <a href="/rapport/{nom_rapport_txt}" class="btn">📝 Télécharger rapport texte</a>
                    {"<a href='/rapport/" + os.path.basename(chemin_pdf) + "' class='btn'>📊 Télécharger rapport PDF</a>" if chemin_pdf else ""}
                </div>
            </div>
        </body>
        </html>
        """

    except Exception as e:
        logger.error(f"Erreur lors du traitement: {str(e)}")
        return f"""
        <!DOCTYPE html>
        <html lang="fr">
        <head>
            <meta charset="UTF-8">
            <title>Erreur</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 600px;
                    margin: 50px auto;
                    padding: 20px;
                    background: #f8f9fa;
                }}
                .error {{
                    background: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                    padding: 20px;
                    border-radius: 10px;
                    margin: 20px 0;
                }}
                .btn {{
                    background: #007bff;
                    color: white;
                    padding: 10px 20px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    margin: 10px;
                }}
            </style>
        </head>
        <body>
            <h1>❌ Erreur lors du traitement</h1>
            <div class="error">
                <p><strong>Erreur:</strong> {str(e)}</p>
                <p>Veuillez vérifier que votre fichier n'est pas corrompu et réessayer.</p>
            </div>
            <a href="/" class="btn">🔄 Réessayer</a>
        </body>
        </html>
        """, 500

@app.route('/rapport/<filename>')
def telecharger_rapport(filename):
    """Permet de télécharger un rapport généré."""
    return send_from_directory(REPORTS_FOLDER, filename)

if __name__ == '__main__':
    logger.info("🚀 Démarrage de l'Agent IA d'Audit Gouvernance IT (version simplifiée)")
    logger.info(f"📧 Emails de destination: {', '.join(EMAIL_DESTINATAIRES)}")
    logger.info("🌐 Application disponible sur: http://localhost:5000")
    app.run(debug=True, port=5000)
