# 🚀 GUIDE DE DÉMARRAGE RAPIDE
## Agent IA d'Audit Gouvernance IT - COBIT 2019

### ✅ PROBLÈMES CORRIGÉS

Tous les problèmes ont été identifiés et corrigés :

1. **❌ Erreur "Template not found"** → ✅ **CORRIGÉ**
   - Fichier `index.html` déplacé dans le dossier `templates/`
   - Structure Flask correctement configurée

2. **❌ Erreurs d'import (langchain, magic)** → ✅ **CORRIGÉ**
   - Version simplifiée créée (`app_simple.py`)
   - Dépendances optionnelles gérées gracieusement
   - Fonctionnement garanti sans dépendances complexes

3. **❌ Problèmes de validation de fichiers** → ✅ **CORRIGÉ**
   - Validation simplifiée mais robuste
   - Gestion d'erreurs améliorée
   - Messages d'erreur clairs pour l'utilisateur

### 🎯 DÉMARRAGE EN 3 ÉTAPES

#### Étape 1: Démarrer l'application
```bash
cd "Desktop\Agent ai cobit"
python app_simple.py
```

#### Étape 2: Ouvrir l'interface
- Ouvrez votre navigateur
- Allez sur: http://localhost:5000

#### Étape 3: Utiliser l'agent
1. Sélectionnez un fichier PDF ou Excel
2. Cliquez sur "🚀 Lancer l'analyse et envoyer le rapport"
3. Le rapport est automatiquement envoyé à `<EMAIL>`

### 📁 FICHIERS PRINCIPAUX

- **`app_simple.py`** : Application principale (VERSION FONCTIONNELLE)
- **`templates/index.html`** : Interface utilisateur
- **`uploads/`** : Dossier temporaire pour les fichiers
- **`reports/`** : Rapports générés et sauvegardés

### 🧪 TESTER L'APPLICATION

```bash
# Test automatique complet
python test_complet.py

# Ou test manuel:
# 1. Démarrer l'app: python app_simple.py
# 2. Ouvrir: http://localhost:5000
# 3. Uploader un fichier PDF/Excel
```

### 📊 FONCTIONNALITÉS GARANTIES

#### ✅ Upload et Traitement
- **Formats supportés**: PDF, Excel (.xlsx, .xls)
- **Taille maximum**: 16MB
- **Validation**: Automatique avec messages d'erreur clairs

#### ✅ Analyse Intelligente
- **Extraction de mots-clés**: Termes de gouvernance IT
- **Score de maturité**: Calcul automatique (0-5)
- **Recommandations**: Court/moyen/long terme

#### ✅ Rapport COBIT 2019
- **Synthèse exécutive**: Score global et priorités
- **Objectifs COBIT**: EDM, APO, BAI, DSS, MEA
- **Analyse ROI**: Investissements et bénéfices estimés
- **Feuille de route**: Plan d'implémentation par phases

#### ✅ Envoi Automatique
- **Email par défaut**: `<EMAIL>`
- **Pas de saisie manuelle**: Complètement automatisé
- **Format professionnel**: Rapport structuré et lisible

### 🔧 DÉPANNAGE

#### Problème: "Module not found"
```bash
# Installer les dépendances de base
pip install flask pandas

# Pour les PDF (optionnel)
pip install PyMuPDF
```

#### Problème: "Port already in use"
```bash
# Changer le port dans app_simple.py ligne finale:
app.run(debug=True, port=5001)  # Au lieu de 5000
```

#### Problème: Email non envoyé
- Vérifiez votre connexion internet
- Les paramètres email sont dans `app_simple.py` (fonction `envoyer_email_simple`)
- Le rapport est quand même généré et téléchargeable

### 📈 EXEMPLE DE RAPPORT GÉNÉRÉ

```
🏢 RAPPORT D'AUDIT GOUVERNANCE IT - COBIT 2019
📅 Date du rapport: 10/07/2025 à 14:30

## 1. 📋 SYNTHÈSE EXÉCUTIVE
- Score de maturité estimé: 2.5/5
- Statut: Amélioration nécessaire

## 2. 🎯 ANALYSE DES BESOINS CLIENT
- Mots-clés identifiés: gouvernance (3x), processus (5x), risques (2x)

## 3. 🏗️ OBJECTIFS COBIT 2019 RECOMMANDÉS
### Domaine EDM (Évaluer, Diriger, Surveiller)
- EDM01: Assurer la définition du cadre de gouvernance (Priorité: Élevée)

## 4. 📊 ÉVALUATION DE MATURITÉ
- Niveau actuel: 2.5/5
- Niveau cible: 4.5/5
- Écart à combler: 2.0 points

## 5. 💡 RECOMMANDATIONS STRATÉGIQUES
### Court terme (0-6 mois)
- Définir les rôles et responsabilités IT
- Mettre en place des politiques de base

## 6. 📈 ANALYSE ROI
- Investissement estimé: 125,000€
- Bénéfices annuels: 187,500€
- ROI estimé: 150%
```

### 🎉 RÉSULTAT FINAL

**L'Agent IA d'Audit Gouvernance IT fonctionne parfaitement !**

✅ **Tous les problèmes ont été corrigés**
✅ **Application testée et fonctionnelle**
✅ **Interface utilisateur moderne et intuitive**
✅ **Envoi automatique à <EMAIL>**
✅ **Rapports COBIT 2019 professionnels**

### 📞 SUPPORT

Si vous rencontrez des problèmes :

1. **Vérifiez les logs** dans la console où vous avez lancé `python app_simple.py`
2. **Testez avec** `python test_complet.py`
3. **Redémarrez l'application** si nécessaire

---

**🎯 Votre Agent IA d'Audit Gouvernance IT est prêt à analyser vos documents !**
