#!/usr/bin/env python3
"""
Script pour corriger le problème Ollama dans rapport_generator.py
"""

# Lire le fichier
with open('rapport_generator.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Corriger la ligne problématique
content = content.replace(
    'llm = Ollama(model="cobit-auditeur")  # Commenté temporairement',
    '# llm = Ollama(model="cobit-auditeur")  # Commenté temporairement'
)

# Ajouter une gestion d'erreur pour Ollama
if '# from langchain.llms import Ollama' in content and 'try:' not in content[:200]:
    content = content.replace(
        '# from langchain.llms import Ollama  # Commenté pour éviter les erreurs d\'import',
        '''# Import Ollama avec gestion d'erreur
try:
    from langchain.llms import Ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    print("⚠️ Ollama/Langchain non disponible - utilisation du MockLLM")'''
    )

# Écrire le fichier corrigé
with open('rapport_generator.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("✅ Fichier rapport_generator.py corrigé !")
