#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier le lancement de l'Agent AI COBIT
"""

import os
import sys
import subprocess
import time
import requests

def test_agent_launch():
    """
    Teste le lancement de l'Agent AI COBIT
    """
    print("🧪 Test de lancement de l'Agent AI COBIT")
    print("=" * 50)
    
    # Chemin vers votre Agent AI
    agent_path = r"C:\Users\<USER>\Desktop\symfcopitejihed\symfcopite\symf\symfcobite\cobit-laravel\Agent ai cobit"
    app_file = os.path.join(agent_path, "app.py")
    
    # Vérifier que le répertoire existe
    print(f"📁 Vérification du répertoire: {agent_path}")
    if not os.path.exists(agent_path):
        print(f"❌ Le répertoire n'existe pas: {agent_path}")
        return False
    print("✅ Répertoire trouvé")
    
    # Vérifier que app.py existe
    print(f"📄 Vérification du fichier: {app_file}")
    if not os.path.exists(app_file):
        print(f"❌ Le fichier app.py n'existe pas: {app_file}")
        return False
    print("✅ Fichier app.py trouvé")
    
    # Vérifier que Python est disponible
    print("🐍 Vérification de Python...")
    try:
        result = subprocess.run(['python', '--version'], capture_output=True, text=True)
        print(f"✅ Python disponible: {result.stdout.strip()}")
    except FileNotFoundError:
        print("❌ Python n'est pas installé ou pas dans le PATH")
        return False
    
    # Tester le lancement de l'Agent AI
    print("\n🚀 Test de lancement de l'Agent AI...")
    try:
        # Changer vers le répertoire de l'Agent AI
        os.chdir(agent_path)
        print(f"📂 Répertoire de travail: {os.getcwd()}")
        
        # Lancer l'Agent AI en arrière-plan
        print("🔄 Lancement de app.py...")
        process = subprocess.Popen(['python', 'app.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # Attendre un peu pour que Flask démarre
        print("⏳ Attente du démarrage de Flask (5 secondes)...")
        time.sleep(5)
        
        # Vérifier si Flask répond
        print("🌐 Test de connexion à http://localhost:5000...")
        try:
            response = requests.get('http://localhost:5000', timeout=5)
            if response.status_code == 200:
                print("✅ Agent AI COBIT répond correctement !")
                print(f"📊 Code de statut: {response.status_code}")
                
                # Arrêter le processus
                process.terminate()
                print("🛑 Processus arrêté")
                return True
            else:
                print(f"⚠️ Agent AI répond mais avec le code: {response.status_code}")
                process.terminate()
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Impossible de se connecter à l'Agent AI: {e}")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        return False

def main():
    """
    Fonction principale
    """
    print("🤖 Test d'intégration Agent AI COBIT")
    print("Développé pour l'intégration Laravel\n")
    
    success = test_agent_launch()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCÈS ! L'Agent AI COBIT fonctionne correctement")
        print("✅ Vous pouvez maintenant utiliser le bouton dans l'interface web")
        print("🌐 URL de l'Agent AI: http://localhost:5000")
    else:
        print("❌ ÉCHEC ! Il y a un problème avec l'Agent AI")
        print("🔧 Vérifiez les erreurs ci-dessus et corrigez-les")
    
    print("\n📝 Instructions:")
    print("1. Allez sur http://localhost:8000/cobit/home")
    print("2. Cliquez sur le bouton 'Agent AI COBIT'")
    print("3. L'interface s'ouvrira automatiquement")

if __name__ == "__main__":
    main()
