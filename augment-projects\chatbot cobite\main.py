"""
Chatbot RAG COBIT 2019 - API FastAPI avec Ollama (Version simplifiée)
Auteur: Assistant IA
Description: Chatbot local pour répondre aux questions sur COBIT 2019
"""

import os
import logging
import json
import re
from typing import Dict, Any, List
from pathlib import Path

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import requests

# Import de la configuration
from config import *

# Configuration du logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Modèles de données Pydantic
class QueryRequest(BaseModel):
    question: str

class QueryResponse(BaseModel):
    response: str

class COBITChatbot:
    """Classe principale du chatbot COBIT 2019 (Version simplifiée)"""

    def __init__(self):
        self.documents = {}
        self.ollama_url = OLLAMA_BASE_URL
        self.model = OLLAMA_MODEL
        self._load_documents()

    def _load_documents(self):
        """Charge tous les documents COBIT depuis le dossier data"""
        try:
            if not DATA_DIR.exists():
                raise FileNotFoundError(f"Le dossier {DATA_DIR} n'existe pas")

            txt_files = list(DATA_DIR.glob("*.txt"))
            if not txt_files:
                raise FileNotFoundError(f"Aucun fichier .txt trouvé dans {DATA_DIR}")

            logger.info(f"Chargement de {len(txt_files)} fichiers...")

            for file_path in txt_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        self.documents[file_path.name] = content
                        logger.info(f"Chargé: {file_path.name}")
                except Exception as e:
                    logger.error(f"Erreur lors du chargement de {file_path}: {e}")

            logger.info(f"Total: {len(self.documents)} documents chargés")

        except Exception as e:
            logger.error(f"Erreur lors du chargement des documents: {e}")
            raise

    def _search_relevant_content(self, question: str, max_results: int = 3) -> List[str]:
        """Recherche simple dans les documents basée sur les mots-clés"""
        question_lower = question.lower()
        relevant_chunks = []

        # Mots-clés importants à rechercher
        keywords = re.findall(r'\b\w+\b', question_lower)

        for filename, content in self.documents.items():
            content_lower = content.lower()

            # Calculer un score de pertinence simple
            score = 0
            for keyword in keywords:
                if len(keyword) > 3:  # Ignorer les mots trop courts
                    score += content_lower.count(keyword)

            if score > 0:
                # Diviser le contenu en chunks
                paragraphs = content.split('\n\n')
                for paragraph in paragraphs:
                    if len(paragraph.strip()) > 50:  # Ignorer les paragraphes trop courts
                        paragraph_lower = paragraph.lower()
                        paragraph_score = 0
                        for keyword in keywords:
                            if len(keyword) > 3:
                                paragraph_score += paragraph_lower.count(keyword)

                        if paragraph_score > 0:
                            relevant_chunks.append((paragraph_score, paragraph.strip()))

        # Trier par score et retourner les meilleurs
        relevant_chunks.sort(key=lambda x: x[0], reverse=True)
        return [chunk[1] for chunk in relevant_chunks[:max_results]]

    def _call_ollama(self, prompt: str) -> str:
        """Appelle l'API Ollama pour générer une réponse"""
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": GENERATION_CONFIG
            }

            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=GENERATION_CONFIG["timeout"]
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "Erreur: Pas de réponse générée")
            else:
                logger.error(f"Erreur Ollama: {response.status_code} - {response.text}")
                return f"Erreur lors de l'appel à Ollama: {response.status_code}"

        except Exception as e:
            logger.error(f"Erreur lors de l'appel à Ollama: {e}")
            return f"Erreur de connexion à Ollama: {str(e)}"
    
    def query(self, question: str) -> str:
        """Traite une question et retourne la réponse"""
        try:
            logger.info(f"Question reçue: {question}")

            # Rechercher le contenu pertinent
            relevant_content = self._search_relevant_content(question)

            if not relevant_content:
                return "Je n'ai pas trouvé d'informations pertinentes dans les documents COBIT 2019 pour répondre à votre question."

            # Construire le prompt avec contexte
            context = "\n\n".join(relevant_content)

            prompt = f"""{SYSTEM_PROMPT}

CONTEXTE COBIT 2019:
{context}

QUESTION: {question}

RÉPONSE:"""

            # Appeler Ollama pour générer la réponse
            response = self._call_ollama(prompt)

            logger.info("Réponse générée avec succès")
            return response

        except Exception as e:
            logger.error(f"Erreur lors du traitement de la question: {e}")
            return f"Erreur lors du traitement de la question: {str(e)}"

# Initialisation du chatbot
chatbot = COBITChatbot()

# Création de l'application FastAPI
app = FastAPI(
    title=WEB_CONFIG["title"],
    description=WEB_CONFIG["description"],
    version=WEB_CONFIG["version"],
    docs_url=WEB_CONFIG["docs_url"],
    redoc_url=WEB_CONFIG["redoc_url"]
)

@app.get("/")
async def root():
    """Point d'entrée de l'API"""
    return {
        "message": "COBIT 2019 RAG Chatbot API",
        "status": "active",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Vérification de l'état de santé de l'API"""
    try:
        # Vérifier que le chatbot est initialisé
        if not chatbot.documents:
            raise RuntimeError("Aucun document chargé")

        # Vérifier la connexion Ollama
        try:
            response = requests.get(f"{chatbot.ollama_url}/api/tags", timeout=5)
            ollama_connected = response.status_code == 200
        except:
            ollama_connected = False

        return {
            "status": "healthy" if ollama_connected else "degraded",
            "ollama_connected": ollama_connected,
            "documents_loaded": len(chatbot.documents),
            "available_documents": list(chatbot.documents.keys())
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service non disponible: {str(e)}")

@app.post("/query", response_model=QueryResponse)
async def query_cobit(request: QueryRequest) -> QueryResponse:
    """
    Point d'entrée principal pour les questions COBIT 2019
    
    Args:
        request: Objet contenant la question
        
    Returns:
        QueryResponse: Réponse générée par le chatbot
    """
    try:
        if not request.question.strip():
            raise HTTPException(status_code=400, detail="La question ne peut pas être vide")
        
        # Traiter la question
        response = chatbot.query(request.question)
        
        return QueryResponse(response=response)
        
    except Exception as e:
        logger.error(f"Erreur lors du traitement de la requête: {e}")
        raise HTTPException(status_code=500, detail=f"Erreur interne: {str(e)}")

if __name__ == "__main__":
    # Vérifier que Ollama est accessible
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Ollama est accessible")
        else:
            logger.warning("⚠️  Ollama ne semble pas être accessible sur localhost:11434")
    except Exception as e:
        logger.warning(f"⚠️  Impossible de vérifier Ollama: {e}")
        logger.warning("Assurez-vous qu'Ollama est démarré avec 'ollama serve'")

    # Lancer le serveur
    logger.info(SYSTEM_MESSAGES["welcome"])
    logger.info(f"📊 Modèle utilisé: {OLLAMA_MODEL}")
    logger.info(f"🌐 Interface web: http://localhost:{SERVER_PORT}/docs")
    uvicorn.run(
        "main:app",
        host=SERVER_HOST,
        port=SERVER_PORT,
        reload=RELOAD,
        log_level=LOG_LEVEL.lower()
    )
