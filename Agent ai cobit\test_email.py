#!/usr/bin/env python3
"""
Test rapide de l'envoi d'email pour diagnostiquer le problème
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from datetime import datetime

def tester_email_simple():
    """Test simple d'envoi d'email."""
    print("🧪 TEST D'ENVOI D'EMAIL")
    print("=" * 40)
    
    # Configuration
    expediteur = '<EMAIL>'
    mot_de_passe = 'jihed200'
    destinataires = ['<EMAIL>', '<EMAIL>']
    
    print(f"📧 Expéditeur: {expediteur}")
    print(f"📧 Destinataires: {', '.join(destinataires)}")
    
    try:
        # Créer le message
        msg = MIMEMultipart()
        msg['From'] = expediteur
        msg['To'] = ', '.join(destinataires)
        msg['Subject'] = f'🧪 Test Email Agent IA - {datetime.now().strftime("%d/%m/%Y %H:%M")}'
        
        corps = f"""
Bonjour,

Ceci est un email de test pour vérifier la configuration de l'Agent IA d'Audit Gouvernance IT.

📅 Date: {datetime.now().strftime("%d/%m/%Y à %H:%M")}
🤖 Générateur: Agent IA Test
✅ Statut: Configuration email fonctionnelle

Si vous recevez cet email, la configuration est correcte !

---
Test automatique de l'Agent IA d'Audit Gouvernance IT
        """
        
        msg.attach(MIMEText(corps, 'plain', 'utf-8'))
        
        # Test 1: SMTP_SSL (port 465)
        print("\n🔐 Test 1: SMTP_SSL (port 465)...")
        try:
            with smtplib.SMTP_SSL('smtp.gmail.com', 465) as server:
                server.login(expediteur, mot_de_passe)
                server.send_message(msg)
                print("✅ Succès avec SMTP_SSL !")
                return True
        except smtplib.SMTPAuthenticationError as e:
            print(f"❌ Échec authentification SSL: {e}")
        except Exception as e:
            print(f"❌ Erreur SSL: {e}")
        
        # Test 2: SMTP avec STARTTLS (port 587)
        print("\n🔓 Test 2: SMTP avec STARTTLS (port 587)...")
        try:
            with smtplib.SMTP('smtp.gmail.com', 587) as server:
                server.starttls()
                server.ehlo()
                server.login(expediteur, mot_de_passe)
                server.send_message(msg)
                print("✅ Succès avec STARTTLS !")
                return True
        except smtplib.SMTPAuthenticationError as e:
            print(f"❌ Échec authentification STARTTLS: {e}")
        except Exception as e:
            print(f"❌ Erreur STARTTLS: {e}")
        
        # Test 3: Diagnostic détaillé
        print("\n🔍 Test 3: Diagnostic détaillé...")
        try:
            server = smtplib.SMTP('smtp.gmail.com', 587)
            server.set_debuglevel(1)  # Mode debug
            server.starttls()
            server.ehlo()
            print("🔐 Tentative de connexion...")
            server.login(expediteur, mot_de_passe)
            print("✅ Connexion réussie !")
            server.quit()
            return True
        except Exception as e:
            print(f"❌ Erreur détaillée: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False

def suggestions_resolution():
    """Affiche les suggestions pour résoudre les problèmes d'email."""
    print("\n" + "=" * 50)
    print("💡 SUGGESTIONS POUR RÉSOUDRE LES PROBLÈMES D'EMAIL")
    print("=" * 50)
    
    print("\n🔐 PROBLÈME D'AUTHENTIFICATION GMAIL:")
    print("1. ⚠️  Gmail a désactivé l'accès aux 'applications moins sécurisées'")
    print("2. 🔑 Vous devez utiliser un 'Mot de passe d'application'")
    
    print("\n📋 ÉTAPES POUR CRÉER UN MOT DE PASSE D'APPLICATION:")
    print("1. 🌐 Allez sur https://myaccount.google.com/")
    print("2. 🔒 Cliquez sur 'Sécurité' dans le menu de gauche")
    print("3. 🔐 Activez la 'Validation en 2 étapes' si ce n'est pas fait")
    print("4. 🔑 Cliquez sur 'Mots de passe des applications'")
    print("5. 📱 Sélectionnez 'Autre (nom personnalisé)'")
    print("6. ✏️  Tapez 'Agent IA Audit' comme nom")
    print("7. 📋 Copiez le mot de passe généré (16 caractères)")
    print("8. 🔄 Remplacez 'jihed200' par ce nouveau mot de passe")
    
    print("\n🔧 SOLUTIONS ALTERNATIVES:")
    print("1. 📧 Utiliser un autre service email (Outlook, Yahoo)")
    print("2. 🌐 Utiliser un service d'email API (SendGrid, Mailgun)")
    print("3. 📱 Configurer OAuth2 pour Gmail")
    
    print("\n⚡ SOLUTION RAPIDE:")
    print("Remplacez dans app_simple.py ligne ~149:")
    print("mot_de_passe = 'jihed200'")
    print("par:")
    print("mot_de_passe = 'VOTRE_MOT_DE_PASSE_APPLICATION_16_CARACTERES'")

def main():
    """Fonction principale de test."""
    success = tester_email_simple()
    
    if not success:
        suggestions_resolution()
    else:
        print("\n🎉 EMAIL ENVOYÉ AVEC SUCCÈS !")
        print("Vérifiez votre boîte de réception.")
    
    return success

if __name__ == "__main__":
    main()
