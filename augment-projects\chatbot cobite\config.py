"""
Configuration du chatbot COBIT 2019
"""

import os
from pathlib import Path

# Configuration des chemins
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
STORAGE_DIR = BASE_DIR / "storage"

# Configuration Ollama
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "gemma2:2b"  # Changez ici pour utiliser un autre modèle

# Modèles disponibles (commentez/décommentez selon vos besoins)
AVAILABLE_MODELS = {
    "gemma2:2b": {
        "name": "Gemma 2B",
        "size": "1.6 GB",
        "description": "Modèle léger et rapide, idéal pour débuter",
        "performance": "Correcte",
        "ram_required": "4-8 GB"
    },
    "mistral": {
        "name": "Mistral 7B",
        "size": "4.1 GB", 
        "description": "Bon équilibre performance/taille",
        "performance": "Bonne",
        "ram_required": "8-16 GB"
    },
    "llama3": {
        "name": "Llama 3 8B",
        "size": "4.7 GB",
        "description": "Très bonnes performances",
        "performance": "Excellente", 
        "ram_required": "16+ GB"
    },
    "llama3:70b": {
        "name": "Llama 3 70B",
        "size": "40+ GB",
        "description": "Performances exceptionnelles",
        "performance": "Exceptionnelle",
        "ram_required": "64+ GB"
    }
}

# Configuration du serveur
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 8001
RELOAD = True

# Configuration de la recherche
MAX_SEARCH_RESULTS = 3
MIN_PARAGRAPH_LENGTH = 50
MIN_KEYWORD_LENGTH = 3

# Configuration de génération
GENERATION_CONFIG = {
    "temperature": 0.7,      # Créativité (0.1 = très factuel, 0.9 = très créatif)
    "top_p": 0.9,           # Diversité du vocabulaire
    "max_tokens": 1000,     # Longueur maximale de la réponse
    "timeout": 60           # Timeout en secondes
}

# Messages système
SYSTEM_MESSAGES = {
    "welcome": "🚀 Chatbot COBIT 2019 démarré avec succès !",
    "ollama_warning": "⚠️  Ollama ne semble pas être accessible. Assurez-vous qu'il est démarré avec 'ollama serve'",
    "model_not_found": "❌ Modèle non trouvé. Téléchargez-le avec 'ollama pull {model}'",
    "no_documents": "❌ Aucun document trouvé dans le dossier data/",
    "documents_loaded": "✅ {count} documents COBIT 2019 chargés"
}

# Configuration des logs
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Prompt système pour le chatbot
SYSTEM_PROMPT = """Tu es un expert en COBIT 2019, le framework de gouvernance et de gestion pour l'information et les technologies associées développé par ISACA.

INSTRUCTIONS IMPORTANTES :
- Utilise UNIQUEMENT les informations fournies dans le contexte
- Sois précis et factuel dans tes réponses
- Mentionne les objectifs de gouvernance (EDM) ou de gestion (APO, BAI, DSS, MEA) pertinents
- Structure tes réponses de manière claire avec des puces ou des numéros si approprié
- Si l'information n'est pas dans le contexte, dis-le clairement
- Évite les informations générales non spécifiques à COBIT 2019

STYLE DE RÉPONSE :
- Professionnel mais accessible
- Utilise des exemples concrets quand possible
- Explique les acronymes la première fois
- Organise l'information de manière logique"""

# Configuration de la recherche par mots-clés
KEYWORD_WEIGHTS = {
    # Mots-clés COBIT importants avec leurs poids
    "cobit": 3,
    "gouvernance": 3,
    "gestion": 3,
    "edm": 4,
    "apo": 4,
    "bai": 4,
    "dss": 4,
    "mea": 4,
    "objectif": 2,
    "processus": 2,
    "enabler": 3,
    "principe": 3,
    "cadre": 2,
    "risque": 2,
    "valeur": 2,
    "performance": 2,
    "conformité": 2,
    "ressources": 2,
    "parties prenantes": 3
}

# Extensions de fichiers supportées
SUPPORTED_EXTENSIONS = [".txt", ".md"]

# Configuration de l'interface web
WEB_CONFIG = {
    "title": "COBIT 2019 RAG Chatbot",
    "description": "Chatbot local basé sur Ollama pour répondre aux questions sur COBIT 2019",
    "version": "1.0.0",
    "docs_url": "/docs",
    "redoc_url": "/redoc"
}
