#!/usr/bin/env python3
"""
Script de configuration et démarrage pour l'Agent IA d'Audit Gouvernance IT
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verifier_python():
    """Vérifie la version de Python."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8+ requis. Version actuelle: %s.%s.%s", 
                    version.major, version.minor, version.micro)
        return False
    logger.info("Version Python OK: %s.%s.%s", version.major, version.minor, version.micro)
    return True

def installer_dependances():
    """Installe les dépendances Python."""
    try:
        logger.info("Installation des dépendances...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        logger.info("Dépendances installées avec succès")
        return True
    except subprocess.CalledProcessError as e:
        logger.error("Erreur lors de l'installation des dépendances: %s", e)
        return False

def configurer_nltk():
    """Configure NLTK avec les ressources nécessaires."""
    try:
        import nltk
        logger.info("Configuration de NLTK...")
        
        # Télécharger les ressources nécessaires
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        
        logger.info("NLTK configuré avec succès")
        return True
    except Exception as e:
        logger.error("Erreur lors de la configuration de NLTK: %s", e)
        return False

def verifier_ollama():
    """Vérifie si Ollama est installé et le modèle est disponible."""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            if 'cobit-auditeur' in result.stdout:
                logger.info("Modèle Ollama 'cobit-auditeur' trouvé")
                return True
            else:
                logger.warning("Modèle 'cobit-auditeur' non trouvé. Vous devrez le créer.")
                logger.info("Commande pour créer le modèle: ollama create cobit-auditeur -f Modelfile")
                return False
        else:
            logger.error("Ollama n'est pas installé ou non accessible")
            return False
    except FileNotFoundError:
        logger.error("Ollama n'est pas installé. Installez-le depuis https://ollama.ai")
        return False

def creer_dossiers():
    """Crée les dossiers nécessaires."""
    dossiers = ['uploads', 'reports', 'logs']
    for dossier in dossiers:
        Path(dossier).mkdir(exist_ok=True)
        logger.info("Dossier créé: %s", dossier)

def tester_configuration():
    """Teste la configuration complète."""
    try:
        # Test d'import des modules principaux
        from rapport_generator import traiter_document
        from email_utils import tester_configuration_email
        
        logger.info("Modules principaux importés avec succès")
        
        # Test de la configuration email
        if tester_configuration_email():
            logger.info("Configuration email OK")
        else:
            logger.warning("Problème de configuration email")
        
        return True
    except ImportError as e:
        logger.error("Erreur d'import: %s", e)
        return False

def afficher_instructions():
    """Affiche les instructions de démarrage."""
    print("\n" + "="*60)
    print("🤖 AGENT IA D'AUDIT GOUVERNANCE IT - CONFIGURATION TERMINÉE")
    print("="*60)
    print("\n📋 INSTRUCTIONS DE DÉMARRAGE:")
    print("1. Assurez-vous qu'Ollama est démarré: ollama serve")
    print("2. Créez le modèle COBIT si nécessaire: ollama create cobit-auditeur -f Modelfile")
    print("3. Démarrez l'application: python app.py")
    print("4. Ouvrez votre navigateur sur: http://localhost:5000")
    print("\n📧 EMAIL DE DESTINATION: <EMAIL>")
    print("\n🎯 FONCTIONNALITÉS:")
    print("- Analyse automatique de documents PDF/Excel")
    print("- Extraction de mots-clés gouvernance IT")
    print("- Évaluation de maturité COBIT 2019")
    print("- Recommandations stratégiques")
    print("- Envoi automatique par email")
    print("\n" + "="*60)

def main():
    """Fonction principale de configuration."""
    logger.info("Démarrage de la configuration de l'Agent IA d'Audit Gouvernance IT")
    
    # Vérifications préalables
    if not verifier_python():
        sys.exit(1)
    
    # Installation et configuration
    if not installer_dependances():
        sys.exit(1)
    
    if not configurer_nltk():
        sys.exit(1)
    
    # Création des dossiers
    creer_dossiers()
    
    # Vérifications optionnelles
    verifier_ollama()
    
    # Test final
    if tester_configuration():
        logger.info("Configuration terminée avec succès!")
        afficher_instructions()
    else:
        logger.error("Problèmes détectés dans la configuration")
        sys.exit(1)

if __name__ == "__main__":
    main()
