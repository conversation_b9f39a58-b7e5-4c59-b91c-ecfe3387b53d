<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Chatbot COBIT 2019</title>
    <meta name="csrf-token" content="test-token">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #00338D;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #002266;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #00338D;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Test du Chatbot COBIT 2019</h1>
        
        <div class="test-section">
            <h3>1. Test de Santé du Chatbot</h3>
            <button class="test-button" onclick="testHealth()">Tester la Santé</button>
            <div id="health-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test de Questions</h3>
            <button class="test-button" onclick="testQuestion('Qu\'est-ce que COBIT 2019 ?')">Question Générale</button>
            <button class="test-button" onclick="testQuestion('Expliquez le Design Factor 1')">Design Factor</button>
            <button class="test-button" onclick="testQuestion('Quels sont les objectifs EDM ?')">Gouvernance</button>
            <div id="question-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Test des Suggestions</h3>
            <button class="test-button" onclick="testSuggestions()">Charger les Suggestions</button>
            <div id="suggestions-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Initialiser le Widget Chatbot</h3>
            <button class="test-button" onclick="initChatbot()">Lancer le Chatbot</button>
            <div id="chatbot-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Test de santé
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Test en cours...';
            
            try {
                const response = await fetch('/cobit/chatbot/health');
                const data = await response.json();
                
                if (data.chatbot_available) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ <strong>Chatbot disponible !</strong><br>
                        Version: ${data.data.version}<br>
                        Type: ${data.data.type}<br>
                        Capacités: ${data.data.capabilities.join(', ')}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Chatbot non disponible';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur: ${error.message}`;
            }
        }

        // Test de question
        async function testQuestion(question) {
            const resultDiv = document.getElementById('question-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = `⏳ Envoi de la question: "${question}"...`;
            
            try {
                const response = await fetch('/cobit/chatbot/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': 'test-token'
                    },
                    body: JSON.stringify({ question: question })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        ✅ <strong>Réponse reçue !</strong><br>
                        <strong>Question:</strong> ${data.question}<br>
                        <strong>Réponse:</strong><br>
                        <div style="white-space: pre-line; margin-top: 10px; padding: 10px; background: white; border-radius: 3px;">
                            ${data.answer}
                        </div>
                        <small>Source: ${data.source} - ${data.timestamp}</small>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `❌ Erreur: ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur: ${error.message}`;
            }
        }

        // Test des suggestions
        async function testSuggestions() {
            const resultDiv = document.getElementById('suggestions-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Chargement des suggestions...';
            
            try {
                const response = await fetch('/cobit/chatbot/suggestions');
                const data = await response.json();
                
                if (data.status === 'success') {
                    resultDiv.className = 'result success';
                    let html = '✅ <strong>Suggestions chargées !</strong><br><br>';
                    
                    data.suggestions.forEach(category => {
                        html += `<strong>${category.category}:</strong><br>`;
                        category.questions.forEach(q => {
                            html += `• ${q}<br>`;
                        });
                        html += '<br>';
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ Erreur lors du chargement des suggestions';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur: ${error.message}`;
            }
        }

        // Initialiser le chatbot
        function initChatbot() {
            const resultDiv = document.getElementById('chatbot-result');
            resultDiv.style.display = 'block';
            
            // Charger le CSS du chatbot
            const cssLink = document.createElement('link');
            cssLink.rel = 'stylesheet';
            cssLink.href = '/css/chatbot.css';
            document.head.appendChild(cssLink);
            
            // Charger le JS du chatbot
            const script = document.createElement('script');
            script.src = '/js/chatbot.js';
            script.onload = function() {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = '✅ <strong>Chatbot initialisé !</strong><br>Le widget devrait apparaître en bas à droite de la page.';
            };
            script.onerror = function() {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ Erreur lors du chargement du script chatbot';
            };
            document.head.appendChild(script);
        }
    </script>
</body>
</html>
