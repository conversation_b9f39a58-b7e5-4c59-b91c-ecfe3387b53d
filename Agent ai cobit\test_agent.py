#!/usr/bin/env python3
"""
Tests unitaires pour l'Agent IA d'Audit Gouvernance IT
"""

import unittest
import os
import tempfile
import json
from unittest.mock import patch, MagicMock
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from validation import (
    valider_fichier, nettoyer_nom_fichier, valider_configuration_email,
    ValidationError, FileValidationError
)
from rapport_generator import (
    extraire_mots_cles_gouvernance, analyser_besoins_client,
    analyser_maturite_cobit, generer_recommandations_strategiques
)
from email_utils import creer_email_html

class TestValidation(unittest.TestCase):
    """Tests pour le module de validation."""
    
    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Nettoyage après les tests."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_nettoyer_nom_fichier(self):
        """Test du nettoyage des noms de fichiers."""
        # Test avec caractères dangereux
        nom_sale = "fichier<>:test|?.pdf"
        nom_propre = nettoyer_nom_fichier(nom_sale)
        self.assertNotIn('<', nom_propre)
        self.assertNotIn('>', nom_propre)
        self.assertNotIn(':', nom_propre)
        
        # Test avec nom trop long
        nom_long = "a" * 200 + ".pdf"
        nom_raccourci = nettoyer_nom_fichier(nom_long)
        self.assertLessEqual(len(nom_raccourci), 100)
        self.assertTrue(nom_raccourci.endswith('.pdf'))
        
        # Test avec nom réservé Windows
        nom_reserve = "CON.pdf"
        nom_securise = nettoyer_nom_fichier(nom_reserve)
        self.assertNotEqual(nom_securise.upper(), "CON.PDF")
    
    def test_valider_configuration_email(self):
        """Test de validation des emails."""
        # Email valide
        est_valide, erreur = valider_configuration_email("<EMAIL>")
        self.assertTrue(est_valide)
        self.assertIsNone(erreur)
        
        # Email invalide
        est_valide, erreur = valider_configuration_email("email_invalide")
        self.assertFalse(est_valide)
        self.assertIsNotNone(erreur)
        
        # Email trop long
        email_long = "a" * 250 + "@example.com"
        est_valide, erreur = valider_configuration_email(email_long)
        self.assertFalse(est_valide)
        self.assertIn("trop longue", erreur)
    
    def test_valider_fichier_inexistant(self):
        """Test avec un fichier qui n'existe pas."""
        chemin_inexistant = os.path.join(self.temp_dir, "inexistant.pdf")
        est_valide, erreur = valider_fichier(chemin_inexistant)
        self.assertFalse(est_valide)
        self.assertIn("n'existe pas", erreur)

class TestRapportGenerator(unittest.TestCase):
    """Tests pour le générateur de rapports."""
    
    def test_extraire_mots_cles_gouvernance(self):
        """Test d'extraction des mots-clés de gouvernance."""
        texte_test = """
        Notre entreprise a besoin d'améliorer sa gouvernance IT.
        Nous devons mettre en place des processus APO et DSS.
        La gestion des risques est une priorité.
        Les objectifs EDM01 et APO02 sont critiques.
        """
        
        mots_cles = extraire_mots_cles_gouvernance(texte_test)
        
        # Vérifier que des mots-clés ont été trouvés
        self.assertIsInstance(mots_cles, dict)
        self.assertIn('processus_cobit', mots_cles)
        
        # Vérifier la présence de mots-clés spécifiques
        processus_trouves = ' '.join(mots_cles.get('processus_cobit', []))
        self.assertIn('gouvernance', processus_trouves.lower())
        self.assertIn('risques', processus_trouves.lower())
    
    def test_analyser_besoins_client(self):
        """Test d'analyse des besoins client."""
        texte_test = """
        Nous avons besoin d'améliorer notre gouvernance IT.
        L'objectif est d'optimiser nos processus métier.
        Il est nécessaire de réduire les risques opérationnels.
        Nous souhaitons améliorer la performance de nos systèmes.
        """
        
        analyse = analyser_besoins_client(texte_test)
        
        # Vérifier la structure de retour
        self.assertIsInstance(analyse, dict)
        self.assertIn('mots_frequents', analyse)
        self.assertIn('phrases_besoins', analyse)
        self.assertIn('nombre_phrases', analyse)
        
        # Vérifier que des phrases de besoins ont été identifiées
        self.assertGreater(len(analyse['phrases_besoins']), 0)
        
        # Vérifier que des mots fréquents ont été trouvés
        self.assertGreater(len(analyse['mots_frequents']), 0)
    
    def test_analyser_maturite_cobit(self):
        """Test d'analyse de maturité COBIT."""
        mots_cles_test = {
            'processus_cobit': ['gouvernance (2x)', 'stratégie (1x)'],
            'objectifs_gouvernance': ['EDM01 (1x)']
        }
        
        contenu_test = "gouvernance stratégie architecture processus"
        
        analyse_maturite = analyser_maturite_cobit(mots_cles_test, contenu_test)
        
        # Vérifier la structure de retour
        self.assertIsInstance(analyse_maturite, dict)
        
        # Si des domaines sont identifiés, vérifier leur structure
        for domaine_code, domaine_data in analyse_maturite.items():
            self.assertIn('nom', domaine_data)
            self.assertIn('score_moyen', domaine_data)
            self.assertIn('objectifs', domaine_data)
            
            for objectif in domaine_data['objectifs']:
                self.assertIn('code', objectif)
                self.assertIn('niveau_actuel', objectif)
                self.assertIn('niveau_cible', objectif)

class TestEmailUtils(unittest.TestCase):
    """Tests pour les utilitaires email."""
    
    def test_creer_email_html(self):
        """Test de création d'email HTML."""
        rapport_test = "Rapport de test avec contenu d'audit"
        nom_fichier_test = "document_test.pdf"
        
        html_email = creer_email_html(rapport_test, nom_fichier_test)
        
        # Vérifier que c'est du HTML valide
        self.assertIn('<!DOCTYPE html>', html_email)
        self.assertIn('<html', html_email)
        self.assertIn('</html>', html_email)
        
        # Vérifier la présence du contenu
        self.assertIn(rapport_test, html_email)
        self.assertIn(nom_fichier_test, html_email)
        
        # Vérifier la présence d'éléments de style
        self.assertIn('<style>', html_email)
        self.assertIn('</style>', html_email)

class TestIntegration(unittest.TestCase):
    """Tests d'intégration du système complet."""
    
    @patch('rapport_generator.llm')
    def test_workflow_complet_simulation(self, mock_llm):
        """Test du workflow complet avec simulation."""
        # Simuler la réponse du LLM
        mock_llm.return_value = "Rapport d'audit simulé avec recommandations COBIT 2019"
        
        # Créer un fichier de test temporaire
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Contenu de test pour l'analyse de gouvernance IT")
            fichier_test = f.name
        
        try:
            # Simuler le traitement (sans vraiment traiter un PDF/Excel)
            from rapport_generator import analyser_besoins_client, extraire_mots_cles_gouvernance
            
            contenu_test = "Gouvernance IT processus COBIT stratégie risques"
            
            # Test des fonctions individuelles
            mots_cles = extraire_mots_cles_gouvernance(contenu_test)
            self.assertIsInstance(mots_cles, dict)
            
            analyse_besoins = analyser_besoins_client(contenu_test)
            self.assertIsInstance(analyse_besoins, dict)
            
        finally:
            # Nettoyage
            if os.path.exists(fichier_test):
                os.unlink(fichier_test)

def creer_fichier_test_pdf():
    """Crée un fichier PDF de test simple."""
    try:
        import fitz
        
        # Créer un PDF simple
        doc = fitz.open()
        page = doc.new_page()
        
        text = """
        RAPPORT DE GOUVERNANCE IT
        
        Notre entreprise souhaite améliorer sa gouvernance IT selon le référentiel COBIT 2019.
        
        Besoins identifiés:
        - Améliorer la gestion des risques IT
        - Optimiser les processus APO (Aligner, Planifier, Organiser)
        - Mettre en place des indicateurs de performance
        - Renforcer la sécurité des systèmes d'information
        
        Objectifs stratégiques:
        - Alignement IT/Métier
        - Création de valeur
        - Gestion des risques
        - Optimisation des ressources
        """
        
        page.insert_text((50, 50), text)
        
        # Sauvegarder dans un fichier temporaire
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        doc.save(temp_file.name)
        doc.close()
        
        return temp_file.name
        
    except ImportError:
        return None

def run_tests():
    """Lance tous les tests."""
    print("🧪 Lancement des tests de l'Agent IA d'Audit Gouvernance IT")
    print("=" * 60)
    
    # Créer une suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les classes de tests
    suite.addTests(loader.loadTestsFromTestCase(TestValidation))
    suite.addTests(loader.loadTestsFromTestCase(TestRapportGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestEmailUtils))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # Lancer les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\n" + "=" * 60)
    print(f"📊 RÉSULTATS DES TESTS:")
    print(f"✅ Tests réussis: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Tests échoués: {len(result.failures)}")
    print(f"🚫 Erreurs: {len(result.errors)}")
    print(f"📈 Taux de réussite: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
