[2025-07-14 11:06:26] local.INFO: 🚀 Utilisation d'Ollama COBIT Expert pour analyse personnalisée  
[2025-07-14 11:06:26] local.INFO: 🚀 Début analyse Ollama pour: AQS  
[2025-07-14 11:06:26] local.INFO: 📝 Prompt construit - Appel Ollama...  
[2025-07-14 11:06:26] local.INFO: 🤖 Appel Ollama Mistral pour analyse de document personnalisée  
[2025-07-14 11:07:16] local.INFO: ⏱️ Durée appel Ollama: 49.72s  
[2025-07-14 11:07:16] local.INFO: ✅ Réponse Ollama reçue (814 caractères)  
[2025-07-14 11:07:16] local.INFO: 📄 Début réponse:  En analysant le document et en prenant en compte les contraintes définies, on peut identifier l'entreprise AQS comme une PME (Petite ou Moyenne Entr...  
[2025-07-14 11:07:16] local.INFO: ✅ Réponse Ollama reçue - Parsing...  
[2025-07-14 11:07:16] local.INFO: 🔍 Parsing de la réponse Ollama pour analyse de document...  
[2025-07-14 11:07:16] local.INFO: 📄 Réponse brute (813 caractères): En analysant le document et en prenant en compte les contraintes définies, on peut identifier l'entreprise AQS comme une PME (Petite ou Moyenne Entreprise) dans le secteur général. Les mots clés p...  
[2025-07-14 11:07:16] local.WARNING: ⚠️ Aucun JSON trouvé dans la réponse Ollama  
[2025-07-14 11:07:16] local.INFO: 🔄 Échec parsing - Retour array vide pour éviter l'erreur de type  
[2025-07-14 11:07:16] local.WARNING: ⚠️ Parsing Ollama échoué ou résultat vide  
[2025-07-14 11:07:16] local.INFO: 🔄 Fallback vers analyse de base  
[2025-07-14 11:07:16] local.INFO: Utilisation de l'analyse de fallback COBIT (Ollama indisponible)  
[2025-07-14 11:07:21] local.INFO: ✅ DF1 pré-rempli par IA avec 4 valeurs: 2, 2, 2...  
[2025-07-14 11:07:21] local.INFO: ✅ DF2 pré-rempli par IA avec 4 valeurs: 2, 2, 2...  
[2025-07-14 11:07:21] local.INFO: ✅ DF3 pré-rempli par IA avec 4 valeurs: 3, 3, 3...  
[2025-07-14 11:07:21] local.INFO: ✅ DF4 pré-rempli par IA avec 4 valeurs: 2, 2, 2...  
[2025-07-14 11:07:21] local.INFO: ✅ DF5 pré-rempli par IA avec 2 valeurs: 1, 1...  
[2025-07-14 11:07:21] local.INFO: ✅ DF6 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
[2025-07-14 11:07:21] local.INFO: ✅ DF7 pré-rempli par IA avec 3 valeurs: 2, 3, 2...  
[2025-07-14 11:07:21] local.INFO: ✅ DF8 pré-rempli par IA avec 2 valeurs: 2, 2...  
[2025-07-14 11:07:21] local.INFO: ✅ DF9 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
[2025-07-14 11:07:21] local.INFO: ✅ DF10 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
