[2025-07-14 10:49:18] local.INFO: 🚀 Utilisation d'Ollama COBIT Expert pour analyse personnalisée  
[2025-07-14 10:49:18] local.INFO: 🚀 Début analyse Ollama pour: pm  
[2025-07-14 10:49:18] local.INFO: 📝 Prompt construit - Appel Ollama...  
[2025-07-14 10:49:18] local.INFO: 🤖 Appel Ollama Mistral pour analyse de document personnalisée  
[2025-07-14 10:49:27] local.INFO: ⏱️ Durée appel Ollama: 8.54s  
[2025-07-14 10:49:27] local.INFO: ✅ Réponse Ollama reçue (173 caractères)  
[2025-07-14 10:49:27] local.INFO: 📄 Début réponse:  {
  "df_scores": {
    "DF1": {"score": 2.5, "reasoning": "Le document mentionne une stratégie générale mais pas suffisamment spécifique pour la ...  
[2025-07-14 10:49:27] local.INFO: ✅ Réponse Ollama reçue - Parsing...  
[2025-07-14 10:49:27] local.INFO: 🔍 Parsing de la réponse Ollama pour analyse de document...  
[2025-07-14 10:49:27] local.INFO: 📄 Réponse brute (172 caractères): {
  "df_scores": {
    "DF1": {"score": 2.5, "reasoning": "Le document mentionne une stratégie générale mais pas suffisamment spécifique pour la moyenne entreprise pm."...  
[2025-07-14 10:49:27] local.INFO: 🔍 Réponse commence par { - Parsing direct...  
[2025-07-14 10:49:27] local.WARNING: ⚠️ Parsing direct échoué - Erreur JSON: Syntax error  
[2025-07-14 10:49:27] local.WARNING: ⚠️ Aucun JSON trouvé dans la réponse Ollama  
[2025-07-14 10:49:27] local.INFO: 🔄 Échec parsing - Retour array vide pour éviter l'erreur de type  
[2025-07-14 10:49:27] local.WARNING: ⚠️ Parsing Ollama échoué ou résultat vide  
[2025-07-14 10:49:27] local.INFO: 🔄 Fallback vers analyse de base  
[2025-07-14 10:49:27] local.INFO: Utilisation de l'analyse de fallback COBIT (Ollama indisponible)  
[2025-07-14 10:49:38] local.INFO: 🚀 Utilisation d'Ollama COBIT Expert pour analyse personnalisée  
[2025-07-14 10:49:38] local.INFO: 🚀 Début analyse Ollama pour: pm  
[2025-07-14 10:49:38] local.INFO: 📝 Prompt construit - Appel Ollama...  
[2025-07-14 10:49:38] local.INFO: 🤖 Appel Ollama Mistral pour analyse de document personnalisée  
[2025-07-14 10:49:48] local.INFO: ⏱️ Durée appel Ollama: 10.12s  
[2025-07-14 10:49:48] local.INFO: ✅ Réponse Ollama reçue (194 caractères)  
[2025-07-14 10:49:48] local.INFO: 📄 Début réponse:  {
  "df_scores": {
    "DF1": {"score": 2.0, "reasoning": "Le document mentionne la nécessité d'une stratégie et de vision pour l'entreprise PM, m...  
[2025-07-14 10:49:48] local.INFO: ✅ Réponse Ollama reçue - Parsing...  
[2025-07-14 10:49:48] local.INFO: 🔍 Parsing de la réponse Ollama pour analyse de document...  
[2025-07-14 10:49:48] local.INFO: 📄 Réponse brute (193 caractères): {
  "df_scores": {
    "DF1": {"score": 2.0, "reasoning": "Le document mentionne la nécessité d'une stratégie et de vision pour l'entreprise PM, mais aucune telle information n'est fournie."...  
[2025-07-14 10:49:48] local.INFO: 🔍 Réponse commence par { - Parsing direct...  
[2025-07-14 10:49:48] local.WARNING: ⚠️ Parsing direct échoué - Erreur JSON: Syntax error  
[2025-07-14 10:49:48] local.WARNING: ⚠️ Aucun JSON trouvé dans la réponse Ollama  
[2025-07-14 10:49:48] local.INFO: 🔄 Échec parsing - Retour array vide pour éviter l'erreur de type  
[2025-07-14 10:49:48] local.WARNING: ⚠️ Parsing Ollama échoué ou résultat vide  
[2025-07-14 10:49:48] local.INFO: 🔄 Fallback vers analyse de base  
[2025-07-14 10:49:48] local.INFO: Utilisation de l'analyse de fallback COBIT (Ollama indisponible)  
