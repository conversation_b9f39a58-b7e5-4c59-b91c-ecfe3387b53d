[2025-07-14 10:53:23] local.INFO: 🚀 Utilisation d'Ollama COBIT Expert pour analyse personnalisée  
[2025-07-14 10:53:23] local.INFO: 🚀 Début analyse Ollama pour: pm  
[2025-07-14 10:53:23] local.INFO: 📝 Prompt construit - Appel Ollama...  
[2025-07-14 10:53:23] local.INFO: 🤖 Appel Ollama Mistral pour analyse de document personnalisée  
[2025-07-14 10:53:32] local.INFO: ⏱️ Durée appel Ollama: 9.05s  
[2025-07-14 10:53:32] local.INFO: ✅ Réponse Ollama reçue (173 caractères)  
[2025-07-14 10:53:32] local.INFO: 📄 Début réponse:  {
  "df_scores": {
    "DF1": {"score": 2.5, "reasoning": "Le document fait référence à une stratégie générale de l'entreprise mais pas spécif...  
[2025-07-14 10:53:32] local.INFO: ✅ Réponse Ollama reçue - Parsing...  
[2025-07-14 10:53:32] local.INFO: 🔍 Parsing de la réponse Ollama pour analyse de document...  
[2025-07-14 10:53:32] local.INFO: 📄 Réponse brute (172 caractères): {
  "df_scores": {
    "DF1": {"score": 2.5, "reasoning": "Le document fait référence à une stratégie générale de l'entreprise mais pas spécifique en matière d'IT."...  
[2025-07-14 10:53:32] local.INFO: 🔍 Réponse commence par { - Parsing direct...  
[2025-07-14 10:53:32] local.WARNING: ⚠️ Parsing direct échoué - Erreur JSON: Syntax error  
[2025-07-14 10:53:32] local.WARNING: ⚠️ Aucun JSON trouvé dans la réponse Ollama  
[2025-07-14 10:53:32] local.INFO: 🔄 Échec parsing - Retour array vide pour éviter l'erreur de type  
[2025-07-14 10:53:32] local.WARNING: ⚠️ Parsing Ollama échoué ou résultat vide  
[2025-07-14 10:53:32] local.INFO: 🔄 Fallback vers analyse de base  
[2025-07-14 10:53:32] local.INFO: Utilisation de l'analyse de fallback COBIT (Ollama indisponible)  
[2025-07-14 10:55:05] local.INFO: ✅ DF1 pré-rempli par IA avec 4 valeurs: 2, 2, 3...  
[2025-07-14 10:55:05] local.INFO: ✅ DF2 pré-rempli par IA avec 4 valeurs: 2, 2, 2...  
[2025-07-14 10:55:05] local.INFO: ✅ DF3 pré-rempli par IA avec 4 valeurs: 3, 2, 2...  
[2025-07-14 10:55:05] local.INFO: ✅ DF4 pré-rempli par IA avec 4 valeurs: 2, 2, 2...  
[2025-07-14 10:55:05] local.INFO: ✅ DF5 pré-rempli par IA avec 2 valeurs: 1, 1...  
[2025-07-14 10:55:05] local.INFO: ✅ DF6 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
[2025-07-14 10:55:05] local.INFO: ✅ DF7 pré-rempli par IA avec 3 valeurs: 2, 2, 2...  
[2025-07-14 10:55:05] local.INFO: ✅ DF8 pré-rempli par IA avec 2 valeurs: 2, 2...  
[2025-07-14 10:55:05] local.INFO: ✅ DF9 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
[2025-07-14 10:55:05] local.INFO: ✅ DF10 pré-rempli par IA avec 3 valeurs: 1, 1, 1...  
