# 🤖 Agent AI COBIT - Intégration Simple

## 📋 Description

Intégration rapide et simple de votre projet Agent AI COBIT dans l'interface web Laravel. Cette solution permet de lancer votre Agent AI directement depuis la page d'accueil COBIT.

## ✅ Ce qui a été ajouté

### 1. Boutons d'accès
- **Bouton dans la section Hero** : "Agent AI COBIT" (vert avec icône robot)
- **Bouton dans les actions** : À côté de "Nouvelle Évaluation" et "Comparer Évaluations"

### 2. Fonctionnalité JavaScript
- Fonction `launchAgentAI()` qui lance votre projet
- Confirmation avant lancement
- Gestion des erreurs avec messages utilisateur

### 3. Route backend simple
- Route POST `/cobit/launch-agent-ai`
- Vérification de l'existence du répertoire
- Lancement en arrière-plan (Windows/Linux)

## 🛠️ Configuration

### Chemin de votre Agent AI

Par défaut, le système cherche votre Agent AI dans :
```
C:\Users\<USER>\Desktop\symfcopitejihed\symfcopite\symf\symfcobite\cobit-laravel\Agent ai cobit
```

### Pour modifier le chemin :

1. **Dans le JavaScript** (ligne 330 de `home.blade.php`) :
```javascript
const agentPath = 'VOTRE_NOUVEAU_CHEMIN_ICI';
```

2. **Dans la route** (ligne 85 de `routes/web.php`) :
```php
$agentPath = $request->input('path', 'VOTRE_CHEMIN_PAR_DEFAUT');
```

### Structure requise de votre projet Agent AI

Votre dossier "Agent ai cobit" doit contenir :
```
Agent ai cobit/
├── main.py (script principal)
├── requirements.txt (optionnel)
└── ... (autres fichiers de votre projet)
```

## 🚀 Utilisation

1. **Accédez à la page** : `http://localhost:8000/cobit/home`
2. **Cliquez sur "Agent AI COBIT"** (bouton vert avec robot)
3. **Confirmez le lancement** dans la popup
4. **Votre Agent AI se lance** en arrière-plan

## 🔧 Personnalisation

### Changer la commande de lancement

Si votre Agent AI utilise autre chose que Python :

**Pour Node.js :**
```php
$command = "start /B node \"{$agentPath}\\index.js\"";
```

**Pour un exécutable :**
```php
$command = "start /B \"{$agentPath}\\agent.exe\"";
```

**Pour un script batch :**
```php
$command = "start /B \"{$agentPath}\\launch.bat\"";
```

### Modifier l'interface

Les boutons sont dans `resources/views/cobit/home.blade.php` :
- **Ligne 79** : Bouton dans la section Hero
- **Ligne 106** : Bouton dans les actions

Vous pouvez modifier :
- Les couleurs (classes CSS)
- Le texte
- L'icône
- La position

## 🐛 Dépannage

### Le bouton ne fait rien
1. Vérifiez la console du navigateur (F12)
2. Assurez-vous que le token CSRF est présent
3. Vérifiez que la route `/cobit/launch-agent-ai` existe

### "Le répertoire n'existe pas"
1. Vérifiez le chemin dans le JavaScript
2. Assurez-vous que le dossier "Agent ai cobit" existe
3. Vérifiez les permissions d'accès

### L'Agent AI ne se lance pas
1. Vérifiez que `main.py` existe dans votre dossier
2. Assurez-vous que Python est installé
3. Testez manuellement : `python "chemin/vers/Agent ai cobit/main.py"`

### Erreur de permissions
1. Exécutez Laravel en tant qu'administrateur (Windows)
2. Vérifiez les permissions du dossier Agent AI
3. Assurez-vous que `exec()` est autorisé en PHP

## 🔒 Sécurité

- ✅ Route protégée par authentification
- ✅ Token CSRF requis
- ✅ Validation du chemin
- ✅ Pas de modification de la base de données

## 📝 Notes importantes

1. **Aucune modification** de la base de données
2. **Interface préservée** - tous les styles existants conservés
3. **Fonctionnalités existantes** intactes
4. **Intégration légère** - seulement quelques lignes ajoutées

## 🎯 Prochaines améliorations possibles

Si vous voulez étendre cette intégration :

1. **Interface de monitoring** pour voir si l'Agent AI fonctionne
2. **Upload de fichiers** directement depuis l'interface web
3. **Affichage des résultats** dans l'interface COBIT
4. **Configuration** via fichier .env
5. **Logs** de l'activité de l'Agent AI

## 📞 Support

En cas de problème :

1. **Vérifiez les logs Laravel** : `storage/logs/laravel.log`
2. **Testez manuellement** votre Agent AI
3. **Vérifiez la console** du navigateur (F12)
4. **Assurez-vous** que tous les chemins sont corrects

---

**Cette intégration est simple, rapide et n'affecte pas le fonctionnement existant de votre application COBIT !** 🎉
