<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaCobitAnalysisService
{
    private string $host;
    private string $model;
    private bool $isAvailable;

    public function __construct()
    {
        $this->host = env('OLLAMA_HOST', 'http://localhost:11434');
        // Utiliser mistral qui suit mieux les instructions pour l'analyse de documents
        $this->model = env('OLLAMA_COBIT_MODEL', 'mistral');
        $this->isAvailable = $this->checkAvailability();
    }

    /**
     * Vérifier la disponibilité d'Ollama
     */
    private function checkAvailability(): bool
    {
        try {
            $response = Http::timeout(3)->get("{$this->host}/api/tags");
            return $response->successful();
        } catch (\Exception $e) {
            Log::warning('Ollama COBIT non disponible: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Analyser un document pour les Design Factors COBIT 2019
     */
    public function analyzeDocumentForDesignFactors(string $content, string $documentType, array $projectContext = []): array
    {
        if (!$this->isAvailable) {
            return $this->getFallbackAnalysis($content, $projectContext);
        }

        try {
            $prompt = $this->buildOptimizedPrompt($content, $documentType, $projectContext);
            $response = $this->callOllamaOptimized($prompt);
            
            if ($response) {
                return $this->parseAndValidateResponse($response, $projectContext);
            }
        } catch (\Exception $e) {
            Log::error('Erreur Ollama COBIT Analysis: ' . $e->getMessage());
        }

        return $this->getFallbackAnalysis($content, $projectContext);
    }

    /**
     * Construire un prompt optimisé pour l'analyse COBIT
     */
    private function buildOptimizedPrompt(string $content, string $documentType, array $context): string
    {
        // Analyser le contenu complet pour une meilleure précision
        $analysisContent = strlen($content) > 3000 ? substr($content, 0, 3000) . "..." : $content;

        // Contexte du projet
        $companySize = $context['taille_entreprise'] ?? 'moyenne';
        $constraints = $context['contraintes'] ?? '';
        $sector = $this->detectSector($content);
        $companyName = $context['nom_entreprise'] ?? 'l\'entreprise';

        return "Vous êtes un consultant COBIT 2019. Analysez ce document d'entreprise et évaluez les 10 Design Factors.

ENTREPRISE: {$companyName}
TAILLE: {$companySize}
SECTEUR: {$sector}
CONTRAINTES: {$constraints}

DOCUMENT:
{$analysisContent}

Analysez le document et donnez des scores de 1.0 à 5.0 pour chaque Design Factor selon le contenu.

Répondez UNIQUEMENT avec ce JSON (remplacez les X.X par vos scores):

{
  \"df_scores\": {
    \"DF1\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF2\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF3\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF4\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF5\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF6\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF7\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF8\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF9\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF10\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"}
  },
  \"maturity_estimate\": X.X
}";
    }

    /**
     * Appeler Ollama avec optimisations de performance
     */
    private function callOllamaOptimized(string $prompt): ?string
    {
        try {
            Log::info('🤖 Appel Ollama COBIT Expert pour analyse personnalisée');

            $response = Http::timeout(30)->post("{$this->host}/api/generate", [
                'model' => $this->model,
                'prompt' => $prompt,
                'stream' => false,
                'options' => [
                    'temperature' => 0.3,  // Un peu plus de créativité pour la personnalisation
                    'top_p' => 0.9,
                    'num_predict' => 1200, // Plus de tokens pour une analyse détaillée
                    'repeat_penalty' => 1.1,
                    'seed' => rand(1, 1000000), // Seed aléatoire pour variabilité
                ]
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $responseText = $data['response'] ?? null;

                if ($responseText) {
                    Log::info('✅ Réponse Ollama reçue: ' . substr($responseText, 0, 200) . '...');
                    return $responseText;
                } else {
                    Log::warning('⚠️ Réponse Ollama vide');
                }
            } else {
                Log::error('❌ Erreur HTTP Ollama: ' . $response->status() . ' - ' . $response->body());
            }
        } catch (\Exception $e) {
            Log::error('❌ Exception Ollama: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Parser et valider la réponse Ollama
     */
    private function parseAndValidateResponse(string $response, array $context): array
    {
        Log::info('🔍 Parsing de la réponse Ollama...');

        // Nettoyer la réponse
        $cleanResponse = trim($response);

        // Chercher le JSON dans la réponse
        $jsonStart = strpos($cleanResponse, '{');
        $jsonEnd = strrpos($cleanResponse, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($cleanResponse, $jsonStart, $jsonEnd - $jsonStart + 1);

            // Nettoyer le JSON (enlever les caractères parasites)
            $jsonStr = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $jsonStr);

            Log::info('📋 JSON extrait: ' . substr($jsonStr, 0, 300) . '...');

            $parsed = json_decode($jsonStr, true);

            if ($parsed && isset($parsed['df_scores'])) {
                Log::info('✅ JSON parsé avec succès - ' . count($parsed['df_scores']) . ' Design Factors trouvés');

                // Valider que tous les DF sont présents
                $expectedDFs = ['DF1', 'DF2', 'DF3', 'DF4', 'DF5', 'DF6', 'DF7', 'DF8', 'DF9', 'DF10'];
                $foundDFs = array_keys($parsed['df_scores']);

                if (count(array_intersect($expectedDFs, $foundDFs)) >= 8) { // Au moins 8 DF sur 10
                    return $this->enhanceWithProjectSpecifics($parsed, $context);
                } else {
                    Log::warning('⚠️ Pas assez de Design Factors dans la réponse: ' . implode(', ', $foundDFs));
                }
            } else {
                Log::warning('⚠️ JSON invalide ou df_scores manquant');
                if ($parsed) {
                    Log::info('🔍 Clés trouvées: ' . implode(', ', array_keys($parsed)));
                } else {
                    Log::error('❌ Erreur JSON: ' . json_last_error_msg());
                }
            }
        } else {
            Log::warning('⚠️ Aucun JSON trouvé dans la réponse');
            Log::info('📄 Réponse brute: ' . substr($cleanResponse, 0, 500));
        }

        // Fallback si parsing échoue
        Log::info('🔄 Utilisation du fallback après échec du parsing');
        return $this->getFallbackAnalysis('', $context);
    }

    /**
     * Améliorer l'analyse avec les spécificités du projet
     */
    private function enhanceWithProjectSpecifics(array $analysis, array $context): array
    {
        $maturityLevel = $analysis['maturity_estimate'] ?? 3.0;

        $enhanced = [
            'df_values' => [],
            'maturity_level' => $maturityLevel,
            'estimated_maturity' => $maturityLevel, // Alias pour compatibilité
            'confidence' => $analysis['confidence_global'] ?? 0.8,
            'project_specifics' => $analysis['project_specifics'] ?? [],
            'ollama_enhanced' => true,
            'analysis_method' => 'Ollama COBIT Expert',
            'personalization' => $this->generatePersonalization($context)
        ];

        // Convertir les scores DF en valeurs pour les paramètres spécifiques de chaque DF
        foreach ($analysis['df_scores'] as $df => $data) {
            $score = max(1, min(5, $data['score']));
            $confidence = $data['confidence'] ?? 0.8;
            $reasoning = $data['reasoning'] ?? '';

            $enhanced['df_values'][$df] = $this->generateDFParameterValues(
                $df,
                $score,
                $confidence,
                $context
            );
        }

        return $enhanced;
    }

    /**
     * Générer des valeurs pour les paramètres d'évaluation d'un DF spécifique
     */
    private function generateContextualObjectiveValues(float $baseScore, float $confidence, string $reasoning, array $context): array
    {
        // Cette méthode est maintenant obsolète, utiliser generateDFParameterValues à la place
        return [];
    }

    /**
     * Générer des valeurs pour les paramètres d'un DF spécifique
     */
    private function generateDFParameterValues(string $dfCode, float $baseScore, float $confidence, array $context): array
    {
        // Récupérer la structure des paramètres pour ce DF
        $dfStructure = $this->getDFStructure($dfCode);
        $paramCount = count($dfStructure['labels']);

        if ($paramCount === 0) {
            // Fallback si pas de structure définie
            return [3.0, 3.0, 3.0, 3.0]; // 4 valeurs par défaut
        }

        $values = [];
        $companySize = $context['taille_entreprise'] ?? 'moyenne';

        // Facteurs d'ajustement selon le contexte
        $sizeMultiplier = $this->getSizeMultiplier($companySize);
        $variationRange = (1 - $confidence) * 0.8; // Variation contrôlée

        for ($i = 0; $i < $paramCount; $i++) {
            $paramLabel = $dfStructure['labels'][$i];
            $paramDefault = $dfStructure['defaults'][$i] ?? 3.0;
            $paramRange = $dfStructure['metadata'];

            // Ajustement contextuel selon le paramètre
            $contextAdjustment = $this->getParameterContextAdjustment($dfCode, $paramLabel, $context);

            // Variation aléatoire contrôlée
            $randomVariation = (rand(-100, 100) / 100) * $variationRange;

            // Score final avec tous les ajustements
            $finalScore = $baseScore + $contextAdjustment + $randomVariation;
            $finalScore *= $sizeMultiplier;

            // Respecter les limites du paramètre
            $minValue = $paramRange['min'] ?? 1;
            $maxValue = $paramRange['max'] ?? 5;

            // Assurer que la valeur est dans les limites
            $finalValue = max($minValue, min($maxValue, $finalScore));

            // Arrondir selon le type de paramètre
            if (isset($paramRange['step']) && $paramRange['step'] == 0.1) {
                $values[] = round($finalValue, 1); // Valeurs décimales pour DF5, DF6, etc.
            } else {
                $values[] = round($finalValue); // Valeurs entières pour DF1, DF2, etc.
            }
        }

        return $values;
    }

    /**
     * Obtenir le multiplicateur selon la taille de l'entreprise
     */
    private function getSizeMultiplier(string $size): float
    {
        return match(strtolower($size)) {
            'petite entreprise (< 100 employés)' => 0.9,
            'moyenne entreprise (100-500 employés)' => 1.0,
            'grande entreprise (500-5000 employés)' => 1.1,
            'très grande entreprise (> 5000 employés)' => 1.2,
            default => 1.0
        };
    }

    /**
     * Détecter le secteur d'activité
     */
    private function detectSector(string $content): string
    {
        $contentLower = strtolower($content);
        
        if (strpos($contentLower, 'banque') !== false || strpos($contentLower, 'finance') !== false) {
            return 'financier';
        } elseif (strpos($contentLower, 'santé') !== false || strpos($contentLower, 'médical') !== false) {
            return 'santé';
        } elseif (strpos($contentLower, 'éducation') !== false || strpos($contentLower, 'université') !== false) {
            return 'éducation';
        } elseif (strpos($contentLower, 'industrie') !== false || strpos($contentLower, 'manufacture') !== false) {
            return 'industriel';
        } elseif (strpos($contentLower, 'service') !== false || strpos($contentLower, 'conseil') !== false) {
            return 'services';
        }
        
        return 'général';
    }

    /**
     * Obtenir le type d'objectif (EDM, APO, BAI, DSS, MEA)
     */
    private function getObjectiveType(int $index): string
    {
        if ($index < 5) return 'EDM';
        if ($index < 19) return 'APO';
        if ($index < 30) return 'BAI';
        if ($index < 36) return 'DSS';
        return 'MEA';
    }

    /**
     * Ajustement selon le type d'objectif et le contexte
     */
    private function getTypeAdjustment(string $type, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        
        // Ajustements contextuels
        if (strpos($constraints, 'budget') !== false) {
            return match($type) {
                'EDM' => 0.2,  // Gouvernance importante avec budget limité
                'APO' => 0.1,  // Planification cruciale
                'BAI' => -0.3, // Moins de nouveaux projets
                'DSS' => 0.0,  // Maintien des services
                'MEA' => 0.1,  // Mesure importante
                default => 0.0
            };
        }
        
        if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'rgpd') !== false) {
            return match($type) {
                'EDM' => 0.3,  // Gouvernance sécurité
                'APO' => 0.2,  // Planification sécurité
                'BAI' => 0.1,  // Implémentation sécurisée
                'DSS' => 0.4,  // Services sécurisés
                'MEA' => 0.2,  // Monitoring sécurité
                default => 0.0
            };
        }
        
        return 0.0;
    }

    /**
     * Générer la personnalisation du projet
     */
    private function generatePersonalization(array $context): array
    {
        return [
            'company_size' => $context['taille_entreprise'] ?? 'non spécifiée',
            'constraints_impact' => $this->analyzeConstraintsImpact($context['contraintes'] ?? ''),
            'sector_considerations' => 'Ajustements sectoriels appliqués',
            'customization_level' => 'Élevé - Analyse personnalisée'
        ];
    }

    /**
     * Analyser l'impact des contraintes
     */
    private function analyzeConstraintsImpact(string $constraints): array
    {
        $impacts = [];
        $constraintsLower = strtolower($constraints);
        
        if (strpos($constraintsLower, 'budget') !== false) {
            $impacts[] = 'Optimisation des coûts prioritaire';
        }
        if (strpos($constraintsLower, 'temps') !== false) {
            $impacts[] = 'Implémentation accélérée requise';
        }
        if (strpos($constraintsLower, 'sécurité') !== false) {
            $impacts[] = 'Renforcement sécurité critique';
        }
        if (strpos($constraintsLower, 'conformité') !== false) {
            $impacts[] = 'Conformité réglementaire prioritaire';
        }
        
        return $impacts ?: ['Aucune contrainte spécifique identifiée'];
    }

    /**
     * Analyse de fallback si Ollama indisponible
     */
    public function getFallbackAnalysis(string $content, array $context): array
    {
        Log::info('Utilisation de l\'analyse de fallback COBIT (Ollama indisponible)');
        
        // Analyse basique mais contextualisée
        $baseScores = $this->calculateBasicScores($content);
        $sizeMultiplier = $this->getSizeMultiplier($context['taille_entreprise'] ?? 'moyenne');
        
        $dfValues = [];
        for ($i = 1; $i <= 10; $i++) {
            $dfCode = "DF{$i}";
            $baseScore = $baseScores[$dfCode] ?? 3.0;
            $adjustedScore = $baseScore * $sizeMultiplier;
            $dfValues[$dfCode] = $this->generateDFParameterValues($dfCode, $adjustedScore, 0.6, $context);
        }

        $maturityLevel = array_sum($baseScores) / count($baseScores);

        return [
            'df_values' => $dfValues,
            'maturity_level' => $maturityLevel,
            'estimated_maturity' => $maturityLevel, // Alias pour compatibilité
            'df_suggestions' => count($dfValues),
            'confidence' => 0.6,
            'project_specifics' => ['Analyse de base avec personnalisation'],
            'ollama_enhanced' => false,
            'analysis_method' => 'Analyse de base contextualisée',
            'personalization' => $this->generatePersonalization($context)
        ];
    }

    /**
     * Calcul des scores de base
     */
    private function calculateBasicScores(string $content): array
    {
        $contentLower = strtolower($content);
        
        $dfKeywords = [
            'DF1' => ['stratégie', 'objectifs', 'vision'],
            'DF2' => ['kpi', 'métriques', 'performance'],
            'DF3' => ['risque', 'sécurité', 'menace'],
            'DF4' => ['enjeux', 'défis', 'problèmes'],
            'DF5' => ['menaces', 'cyberattaque', 'vulnérabilité'],
            'DF6' => ['conformité', 'réglementation', 'audit'],
            'DF7' => ['rôle', 'positionnement', 'fonction'],
            'DF8' => ['sourcing', 'externalisation', 'fournisseur'],
            'DF9' => ['méthodes', 'agile', 'implémentation'],
            'DF10' => ['taille', 'complexité', 'envergure']
        ];

        $scores = [];
        foreach ($dfKeywords as $df => $keywords) {
            $score = 2.0; // Score de base
            foreach ($keywords as $keyword) {
                $score += substr_count($contentLower, $keyword) * 0.3;
            }
            $scores[$df] = min(5, max(1, $score));
        }

        return $scores;
    }

    /**
     * Obtenir la structure d'un Design Factor
     */
    private function getDFStructure(string $dfCode): array
    {
        // Structure des Design Factors selon le seeder
        $dfStructures = [
            'DF1' => [
                'labels' => ['Croissance', 'Stabilité', 'Coût', 'Innovation'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF2' => [
                'labels' => ['Portefeuille agile', 'Risques métier', 'Conformité réglementaire', 'Objectif 4'],
                'defaults' => [1, 1, 1, 1],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF3' => [
                'labels' => ['Investissement IT', 'Gestion programmes', 'Coûts IT', 'Expertise IT'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF4' => [
                'labels' => ['Problème IT 1', 'Problème IT 2', 'Problème IT 3', 'Problème IT 4'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF5' => [
                'labels' => ['Menaces externes', 'Menaces internes'],
                'defaults' => [0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF6' => [
                'labels' => ['Exigences réglementaires', 'Exigences sectorielles', 'Exigences internes'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF7' => [
                'labels' => ['Support', 'Factory', 'Turnaround'],
                'defaults' => [3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF8' => [
                'labels' => ['Modèle interne', 'Modèle externe'],
                'defaults' => [1, 1],
                'metadata' => ['min' => 1, 'max' => 3, 'step' => 1]
            ],
            'DF9' => [
                'labels' => ['Méthodes agiles', 'DevOps', 'Méthodes traditionnelles'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF10' => [
                'labels' => ['Petite entreprise', 'Moyenne entreprise', 'Grande entreprise'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ]
        ];

        return $dfStructures[$dfCode] ?? $dfStructures['DF1'];
    }

    /**
     * Ajustement contextuel pour un paramètre spécifique
     */
    private function getParameterContextAdjustment(string $dfCode, string $paramLabel, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        $size = strtolower($context['taille_entreprise'] ?? '');

        $adjustment = 0.0;

        // Ajustements spécifiques par DF et paramètre
        switch ($dfCode) {
            case 'DF1': // Enterprise Strategy
                if (strpos($paramLabel, 'Croissance') !== false && strpos($constraints, 'croissance') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Innovation') !== false && strpos($constraints, 'innovation') !== false) {
                    $adjustment += 0.8;
                }
                if (strpos($paramLabel, 'Coût') !== false && strpos($constraints, 'budget') !== false) {
                    $adjustment += 1.2;
                }
                break;

            case 'DF2': // Enterprise Goals
                if (strpos($paramLabel, 'Conformité') !== false && strpos($constraints, 'conformité') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Risques') !== false && strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.8;
                }
                break;

            case 'DF3': // Risk Profile
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.5; // Augmenter tous les paramètres de risque
                }
                break;

            case 'DF5': // Threat Landscape
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'cyber') !== false) {
                    $adjustment += 0.3; // Menaces plus élevées
                }
                break;

            case 'DF6': // Compliance Requirements
                if (strpos($constraints, 'conformité') !== false || strpos($constraints, 'rgpd') !== false) {
                    $adjustment += 0.4;
                }
                break;

            case 'DF8': // Sourcing Model
                if (strpos($constraints, 'équipe') !== false && strpos($constraints, 'réduite') !== false) {
                    if (strpos($paramLabel, 'externe') !== false) {
                        $adjustment += 1.0; // Plus d'externalisation si équipe réduite
                    }
                }
                break;

            case 'DF9': // Implementation Methods
                if (strpos($constraints, 'agile') !== false && strpos($paramLabel, 'agiles') !== false) {
                    $adjustment += 0.3;
                }
                if (strpos($constraints, 'devops') !== false && strpos($paramLabel, 'DevOps') !== false) {
                    $adjustment += 0.3;
                }
                break;

            case 'DF10': // Enterprise Size
                if (strpos($size, 'petite') !== false && strpos($paramLabel, 'Petite') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'moyenne') !== false && strpos($paramLabel, 'Moyenne') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'grande') !== false && strpos($paramLabel, 'Grande') !== false) {
                    $adjustment += 0.4;
                }
                break;
        }

        return $adjustment;
    }

    /**
     * Vérifier si Ollama est disponible
     */
    public function isAvailable(): bool
    {
        return $this->isAvailable;
    }
}
