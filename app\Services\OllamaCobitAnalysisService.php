<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaCobitAnalysisService
{
    private string $host;
    private string $model;
    private bool $isAvailable;

    public function __construct()
    {
        $this->host = env('OLLAMA_HOST', 'http://localhost:11434');
        // Utiliser mistral qui suit mieux les instructions pour l'analyse de documents
        $this->model = env('OLLAMA_COBIT_MODEL', 'mistral');
        $this->isAvailable = $this->checkAvailability();
    }

    /**
     * Vérifier la disponibilité d'Ollama
     */
    private function checkAvailability(): bool
    {
        try {
            $response = Http::timeout(3)->get("{$this->host}/api/tags");
            return $response->successful();
        } catch (\Exception $e) {
            Log::warning('Ollama COBIT non disponible: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Analyser un document pour les Design Factors COBIT 2019
     */
    public function analyzeDocumentForDesignFactors(string $content, string $documentType, array $projectContext = []): array
    {
        Log::info('🚀 Début analyse Ollama pour: ' . ($projectContext['nom_entreprise'] ?? 'Entreprise'));

        if (!$this->isAvailable) {
            Log::warning('⚠️ Ollama indisponible - Utilisation du fallback');
            return $this->getFallbackAnalysis($content, $projectContext);
        }

        try {
            // Construire un prompt spécialisé pour l'analyse de document
            $prompt = $this->buildDocumentAnalysisPrompt($content, $documentType, $projectContext);

            Log::info('📝 Prompt construit - Appel Ollama...');
            $response = $this->callOllamaForDocumentAnalysis($prompt);

            if ($response) {
                Log::info('✅ Réponse Ollama reçue - Parsing...');
                $result = $this->parseOllamaResponse($response, $projectContext);

                if ($result && isset($result['df_values'])) {
                    Log::info('🎯 Analyse Ollama réussie - ' . count($result['df_values']) . ' DF générés');
                    return $result;
                }
            }
        } catch (\Exception $e) {
            Log::error('❌ Erreur Ollama COBIT Analysis: ' . $e->getMessage());
        }

        Log::info('🔄 Fallback vers analyse de base');
        return $this->getFallbackAnalysis($content, $projectContext);
    }

    /**
     * Construire un prompt spécialisé pour l'analyse de document
     */
    private function buildDocumentAnalysisPrompt(string $content, string $documentType, array $context): string
    {
        // Analyser le contenu complet pour une meilleure précision
        $analysisContent = strlen($content) > 2500 ? substr($content, 0, 2500) . "\n[...document tronqué...]" : $content;

        // Contexte du projet
        $companySize = $context['taille_entreprise'] ?? 'moyenne entreprise';
        $constraints = $context['contraintes'] ?? 'aucune contrainte spécifiée';
        $companyName = $context['nom_entreprise'] ?? 'l\'entreprise';

        // Analyser le secteur depuis le contenu
        $sector = $this->detectSector($content);

        return "ANALYSE COBIT 2019 - ÉVALUATION PERSONNALISÉE

Vous êtes un expert consultant COBIT 2019. Analysez ce document réel d'entreprise et évaluez les 10 Design Factors avec des scores personnalisés basés sur le contenu spécifique.

CONTEXTE ENTREPRISE:
- Nom: {$companyName}
- Taille: {$companySize}
- Secteur détecté: {$sector}
- Contraintes: {$constraints}

DOCUMENT À ANALYSER:
{$analysisContent}

MISSION:
1. Lisez attentivement le document
2. Identifiez les éléments spécifiques à cette entreprise
3. Évaluez chaque Design Factor de 1.0 à 5.0 selon le contenu réel
4. Justifiez chaque score par des éléments concrets du document

DESIGN FACTORS À ÉVALUER:
- DF1 (Enterprise Strategy): Stratégie et vision décrites
- DF2 (Enterprise Goals): Objectifs et KPI mentionnés
- DF3 (Risk Profile): Risques et sécurité évoqués
- DF4 (IT Issues): Enjeux et défis IT identifiés
- DF5 (Threat Landscape): Menaces et vulnérabilités
- DF6 (Compliance): Conformité et réglementations
- DF7 (Role of IT): Rôle et positionnement IT
- DF8 (Sourcing Model): Modèle d'approvisionnement
- DF9 (Implementation Methods): Méthodes et processus
- DF10 (Enterprise Size): Taille confirmée par le contenu

IMPORTANT: Variez les scores selon le contenu spécifique. Ne donnez pas des scores génériques.

Répondez UNIQUEMENT avec ce JSON valide:
{
  \"df_scores\": {
    \"DF1\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF2\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF3\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF4\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF5\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF6\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF7\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF8\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF9\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"},
    \"DF10\": {\"score\": 0.0, \"reasoning\": \"Analyse basée sur le document\"}
  },
  \"maturity_estimate\": 0.0,
  \"confidence_global\": 0.8
}";
    }

    /**
     * Appeler Ollama spécialement pour l'analyse de documents
     */
    private function callOllamaForDocumentAnalysis(string $prompt): ?string
    {
        try {
            Log::info('🤖 Appel Ollama Mistral pour analyse de document personnalisée');

            $startTime = microtime(true);

            $response = Http::timeout(60)->post("{$this->host}/api/generate", [
                'model' => $this->model,
                'prompt' => $prompt,
                'stream' => false,
                'options' => [
                    'temperature' => 0.4,  // Équilibre créativité/précision
                    'top_p' => 0.9,
                    'top_k' => 40,
                    'num_predict' => 1500, // Plus de tokens pour analyse complète
                    'repeat_penalty' => 1.1,
                    'seed' => time() + rand(1, 1000), // Seed basé sur le temps pour variabilité
                    'stop' => ['}', '\n\n\n'], // Arrêt après JSON complet
                ]
            ]);

            $duration = round((microtime(true) - $startTime), 2);
            Log::info("⏱️ Durée appel Ollama: {$duration}s");

            if ($response->successful()) {
                $data = $response->json();
                $responseText = $data['response'] ?? null;

                if ($responseText) {
                    Log::info('✅ Réponse Ollama reçue (' . strlen($responseText) . ' caractères)');
                    Log::info('📄 Début réponse: ' . substr($responseText, 0, 150) . '...');
                    return $responseText;
                } else {
                    Log::warning('⚠️ Réponse Ollama vide dans les données');
                }
            } else {
                Log::error('❌ Erreur HTTP Ollama: ' . $response->status());
                Log::error('📄 Corps erreur: ' . substr($response->body(), 0, 300));
            }
        } catch (\Exception $e) {
            Log::error('❌ Exception lors de l\'appel Ollama: ' . $e->getMessage());
            Log::error('📍 Trace: ' . $e->getTraceAsString());
        }

        return null;
    }

    /**
     * Parser la réponse Ollama pour l'analyse de document
     */
    private function parseOllamaResponse(string $response, array $context): array
    {
        Log::info('🔍 Parsing de la réponse Ollama pour analyse de document...');

        // Nettoyer la réponse
        $cleanResponse = trim($response);

        // Chercher le JSON dans la réponse (peut être entouré de texte)
        $jsonStart = strpos($cleanResponse, '{');
        $jsonEnd = strrpos($cleanResponse, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($cleanResponse, $jsonStart, $jsonEnd - $jsonStart + 1);

            // Nettoyer le JSON
            $jsonStr = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $jsonStr);
            $jsonStr = str_replace(['\n', '\r', '\t'], ['', '', ''], $jsonStr);

            Log::info('📋 JSON extrait (' . strlen($jsonStr) . ' caractères): ' . substr($jsonStr, 0, 200) . '...');

            $parsed = json_decode($jsonStr, true);

            if ($parsed && isset($parsed['df_scores'])) {
                $dfCount = count($parsed['df_scores']);
                Log::info("✅ JSON parsé avec succès - {$dfCount} Design Factors trouvés");

                // Valider les scores
                $validScores = 0;
                foreach ($parsed['df_scores'] as $df => $data) {
                    if (isset($data['score']) && is_numeric($data['score'])) {
                        $validScores++;
                    }
                }

                if ($validScores >= 8) { // Au moins 8 DF valides sur 10
                    Log::info("🎯 {$validScores} scores valides trouvés - Conversion en paramètres DF");
                    return $this->convertOllamaScoresToDFParameters($parsed, $context);
                } else {
                    Log::warning("⚠️ Pas assez de scores valides: {$validScores}/10");
                }
            } else {
                Log::warning('⚠️ JSON invalide ou df_scores manquant');
                if ($parsed) {
                    Log::info('🔍 Clés trouvées dans JSON: ' . implode(', ', array_keys($parsed)));
                } else {
                    Log::error('❌ Erreur parsing JSON: ' . json_last_error_msg());
                }
            }
        } else {
            Log::warning('⚠️ Aucun JSON trouvé dans la réponse Ollama');
            Log::info('📄 Début de réponse: ' . substr($cleanResponse, 0, 300) . '...');
        }

        // Fallback si parsing échoue
        Log::info('🔄 Échec parsing - Utilisation du fallback');
        return null;
    }

    /**
     * Convertir les scores Ollama en paramètres DF spécifiques
     */
    private function convertOllamaScoresToDFParameters(array $ollamaResult, array $context): array
    {
        Log::info('🔄 Conversion des scores Ollama en paramètres DF...');

        $dfValues = [];
        $totalScore = 0;
        $scoreCount = 0;

        foreach ($ollamaResult['df_scores'] as $df => $data) {
            $score = floatval($data['score'] ?? 3.0);
            $reasoning = $data['reasoning'] ?? 'Analyse Ollama';

            // Convertir le score global en valeurs pour les paramètres spécifiques du DF
            $dfValues[$df] = $this->generateDFParameterValues($df, $score, 0.9, $context);

            $totalScore += $score;
            $scoreCount++;

            Log::info("📊 {$df}: Score {$score}/5 → " . count($dfValues[$df]) . " paramètres générés");
        }

        $averageMaturity = $scoreCount > 0 ? round($totalScore / $scoreCount, 2) : 3.0;

        $result = [
            'df_values' => $dfValues,
            'maturity_level' => $averageMaturity,
            'estimated_maturity' => $averageMaturity,
            'df_suggestions' => count($dfValues),
            'confidence' => 0.9, // Haute confiance car analysé par Ollama
            'project_specifics' => $ollamaResult['project_specifics'] ?? ['Analyse personnalisée par Ollama'],
            'ollama_enhanced' => true,
            'analysis_method' => 'Ollama Mistral - Analyse de document',
            'personalization' => [
                'company_size' => $context['taille_entreprise'] ?? 'non spécifiée',
                'company_name' => $context['nom_entreprise'] ?? 'non spécifiée',
                'constraints' => $context['contraintes'] ?? 'aucune',
                'customization_level' => 'Très élevé - Analyse Ollama personnalisée'
            ]
        ];

        Log::info("✅ Conversion terminée - Maturité: {$averageMaturity}/5, {$scoreCount} DF traités");

        return $result;
    }

    /**
     * Améliorer l'analyse avec les spécificités du projet (méthode legacy)
     */
    private function enhanceWithProjectSpecifics(array $analysis, array $context): array
    {
        $maturityLevel = $analysis['maturity_estimate'] ?? 3.0;

        $enhanced = [
            'df_values' => [],
            'maturity_level' => $maturityLevel,
            'estimated_maturity' => $maturityLevel, // Alias pour compatibilité
            'confidence' => $analysis['confidence_global'] ?? 0.8,
            'project_specifics' => $analysis['project_specifics'] ?? [],
            'ollama_enhanced' => true,
            'analysis_method' => 'Ollama COBIT Expert',
            'personalization' => $this->generatePersonalization($context)
        ];

        // Convertir les scores DF en valeurs pour les paramètres spécifiques de chaque DF
        foreach ($analysis['df_scores'] as $df => $data) {
            $score = max(1, min(5, $data['score']));
            $confidence = $data['confidence'] ?? 0.8;
            $reasoning = $data['reasoning'] ?? '';

            $enhanced['df_values'][$df] = $this->generateDFParameterValues(
                $df,
                $score,
                $confidence,
                $context
            );
        }

        return $enhanced;
    }

    /**
     * Générer des valeurs pour les paramètres d'évaluation d'un DF spécifique
     */
    private function generateContextualObjectiveValues(float $baseScore, float $confidence, string $reasoning, array $context): array
    {
        // Cette méthode est maintenant obsolète, utiliser generateDFParameterValues à la place
        return [];
    }

    /**
     * Générer des valeurs pour les paramètres d'un DF spécifique
     */
    private function generateDFParameterValues(string $dfCode, float $baseScore, float $confidence, array $context): array
    {
        // Récupérer la structure des paramètres pour ce DF
        $dfStructure = $this->getDFStructure($dfCode);
        $paramCount = count($dfStructure['labels']);

        if ($paramCount === 0) {
            // Fallback si pas de structure définie
            return [3.0, 3.0, 3.0, 3.0]; // 4 valeurs par défaut
        }

        $values = [];
        $companySize = $context['taille_entreprise'] ?? 'moyenne';

        // Facteurs d'ajustement selon le contexte
        $sizeMultiplier = $this->getSizeMultiplier($companySize);
        $variationRange = (1 - $confidence) * 0.8; // Variation contrôlée

        for ($i = 0; $i < $paramCount; $i++) {
            $paramLabel = $dfStructure['labels'][$i];
            $paramDefault = $dfStructure['defaults'][$i] ?? 3.0;
            $paramRange = $dfStructure['metadata'];

            // Ajustement contextuel selon le paramètre
            $contextAdjustment = $this->getParameterContextAdjustment($dfCode, $paramLabel, $context);

            // Variation aléatoire contrôlée
            $randomVariation = (rand(-100, 100) / 100) * $variationRange;

            // Score final avec tous les ajustements
            $finalScore = $baseScore + $contextAdjustment + $randomVariation;
            $finalScore *= $sizeMultiplier;

            // Respecter les limites du paramètre
            $minValue = $paramRange['min'] ?? 1;
            $maxValue = $paramRange['max'] ?? 5;

            // Assurer que la valeur est dans les limites
            $finalValue = max($minValue, min($maxValue, $finalScore));

            // Arrondir selon le type de paramètre
            if (isset($paramRange['step']) && $paramRange['step'] == 0.1) {
                $values[] = round($finalValue, 1); // Valeurs décimales pour DF5, DF6, etc.
            } else {
                $values[] = round($finalValue); // Valeurs entières pour DF1, DF2, etc.
            }
        }

        return $values;
    }

    /**
     * Obtenir le multiplicateur selon la taille de l'entreprise
     */
    private function getSizeMultiplier(string $size): float
    {
        return match(strtolower($size)) {
            'petite entreprise (< 100 employés)' => 0.9,
            'moyenne entreprise (100-500 employés)' => 1.0,
            'grande entreprise (500-5000 employés)' => 1.1,
            'très grande entreprise (> 5000 employés)' => 1.2,
            default => 1.0
        };
    }

    /**
     * Détecter le secteur d'activité
     */
    private function detectSector(string $content): string
    {
        $contentLower = strtolower($content);
        
        if (strpos($contentLower, 'banque') !== false || strpos($contentLower, 'finance') !== false) {
            return 'financier';
        } elseif (strpos($contentLower, 'santé') !== false || strpos($contentLower, 'médical') !== false) {
            return 'santé';
        } elseif (strpos($contentLower, 'éducation') !== false || strpos($contentLower, 'université') !== false) {
            return 'éducation';
        } elseif (strpos($contentLower, 'industrie') !== false || strpos($contentLower, 'manufacture') !== false) {
            return 'industriel';
        } elseif (strpos($contentLower, 'service') !== false || strpos($contentLower, 'conseil') !== false) {
            return 'services';
        }
        
        return 'général';
    }

    /**
     * Obtenir le type d'objectif (EDM, APO, BAI, DSS, MEA)
     */
    private function getObjectiveType(int $index): string
    {
        if ($index < 5) return 'EDM';
        if ($index < 19) return 'APO';
        if ($index < 30) return 'BAI';
        if ($index < 36) return 'DSS';
        return 'MEA';
    }

    /**
     * Ajustement selon le type d'objectif et le contexte
     */
    private function getTypeAdjustment(string $type, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        
        // Ajustements contextuels
        if (strpos($constraints, 'budget') !== false) {
            return match($type) {
                'EDM' => 0.2,  // Gouvernance importante avec budget limité
                'APO' => 0.1,  // Planification cruciale
                'BAI' => -0.3, // Moins de nouveaux projets
                'DSS' => 0.0,  // Maintien des services
                'MEA' => 0.1,  // Mesure importante
                default => 0.0
            };
        }
        
        if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'rgpd') !== false) {
            return match($type) {
                'EDM' => 0.3,  // Gouvernance sécurité
                'APO' => 0.2,  // Planification sécurité
                'BAI' => 0.1,  // Implémentation sécurisée
                'DSS' => 0.4,  // Services sécurisés
                'MEA' => 0.2,  // Monitoring sécurité
                default => 0.0
            };
        }
        
        return 0.0;
    }

    /**
     * Générer la personnalisation du projet
     */
    private function generatePersonalization(array $context): array
    {
        return [
            'company_size' => $context['taille_entreprise'] ?? 'non spécifiée',
            'constraints_impact' => $this->analyzeConstraintsImpact($context['contraintes'] ?? ''),
            'sector_considerations' => 'Ajustements sectoriels appliqués',
            'customization_level' => 'Élevé - Analyse personnalisée'
        ];
    }

    /**
     * Analyser l'impact des contraintes
     */
    private function analyzeConstraintsImpact(string $constraints): array
    {
        $impacts = [];
        $constraintsLower = strtolower($constraints);
        
        if (strpos($constraintsLower, 'budget') !== false) {
            $impacts[] = 'Optimisation des coûts prioritaire';
        }
        if (strpos($constraintsLower, 'temps') !== false) {
            $impacts[] = 'Implémentation accélérée requise';
        }
        if (strpos($constraintsLower, 'sécurité') !== false) {
            $impacts[] = 'Renforcement sécurité critique';
        }
        if (strpos($constraintsLower, 'conformité') !== false) {
            $impacts[] = 'Conformité réglementaire prioritaire';
        }
        
        return $impacts ?: ['Aucune contrainte spécifique identifiée'];
    }

    /**
     * Analyse de fallback si Ollama indisponible
     */
    public function getFallbackAnalysis(string $content, array $context): array
    {
        Log::info('Utilisation de l\'analyse de fallback COBIT (Ollama indisponible)');
        
        // Analyse basique mais contextualisée
        $baseScores = $this->calculateBasicScores($content);
        $sizeMultiplier = $this->getSizeMultiplier($context['taille_entreprise'] ?? 'moyenne');
        
        $dfValues = [];
        for ($i = 1; $i <= 10; $i++) {
            $dfCode = "DF{$i}";
            $baseScore = $baseScores[$dfCode] ?? 3.0;
            $adjustedScore = $baseScore * $sizeMultiplier;
            $dfValues[$dfCode] = $this->generateDFParameterValues($dfCode, $adjustedScore, 0.6, $context);
        }

        $maturityLevel = array_sum($baseScores) / count($baseScores);

        return [
            'df_values' => $dfValues,
            'maturity_level' => $maturityLevel,
            'estimated_maturity' => $maturityLevel, // Alias pour compatibilité
            'df_suggestions' => count($dfValues),
            'confidence' => 0.6,
            'project_specifics' => ['Analyse de base avec personnalisation'],
            'ollama_enhanced' => false,
            'analysis_method' => 'Analyse de base contextualisée',
            'personalization' => $this->generatePersonalization($context)
        ];
    }

    /**
     * Calcul des scores de base
     */
    private function calculateBasicScores(string $content): array
    {
        $contentLower = strtolower($content);
        
        $dfKeywords = [
            'DF1' => ['stratégie', 'objectifs', 'vision'],
            'DF2' => ['kpi', 'métriques', 'performance'],
            'DF3' => ['risque', 'sécurité', 'menace'],
            'DF4' => ['enjeux', 'défis', 'problèmes'],
            'DF5' => ['menaces', 'cyberattaque', 'vulnérabilité'],
            'DF6' => ['conformité', 'réglementation', 'audit'],
            'DF7' => ['rôle', 'positionnement', 'fonction'],
            'DF8' => ['sourcing', 'externalisation', 'fournisseur'],
            'DF9' => ['méthodes', 'agile', 'implémentation'],
            'DF10' => ['taille', 'complexité', 'envergure']
        ];

        $scores = [];
        foreach ($dfKeywords as $df => $keywords) {
            $score = 2.0; // Score de base
            foreach ($keywords as $keyword) {
                $score += substr_count($contentLower, $keyword) * 0.3;
            }
            $scores[$df] = min(5, max(1, $score));
        }

        return $scores;
    }

    /**
     * Obtenir la structure d'un Design Factor
     */
    private function getDFStructure(string $dfCode): array
    {
        // Structure des Design Factors selon le seeder
        $dfStructures = [
            'DF1' => [
                'labels' => ['Croissance', 'Stabilité', 'Coût', 'Innovation'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF2' => [
                'labels' => ['Portefeuille agile', 'Risques métier', 'Conformité réglementaire', 'Objectif 4'],
                'defaults' => [1, 1, 1, 1],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF3' => [
                'labels' => ['Investissement IT', 'Gestion programmes', 'Coûts IT', 'Expertise IT'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF4' => [
                'labels' => ['Problème IT 1', 'Problème IT 2', 'Problème IT 3', 'Problème IT 4'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF5' => [
                'labels' => ['Menaces externes', 'Menaces internes'],
                'defaults' => [0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF6' => [
                'labels' => ['Exigences réglementaires', 'Exigences sectorielles', 'Exigences internes'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF7' => [
                'labels' => ['Support', 'Factory', 'Turnaround'],
                'defaults' => [3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF8' => [
                'labels' => ['Modèle interne', 'Modèle externe'],
                'defaults' => [1, 1],
                'metadata' => ['min' => 1, 'max' => 3, 'step' => 1]
            ],
            'DF9' => [
                'labels' => ['Méthodes agiles', 'DevOps', 'Méthodes traditionnelles'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF10' => [
                'labels' => ['Petite entreprise', 'Moyenne entreprise', 'Grande entreprise'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ]
        ];

        return $dfStructures[$dfCode] ?? $dfStructures['DF1'];
    }

    /**
     * Ajustement contextuel pour un paramètre spécifique
     */
    private function getParameterContextAdjustment(string $dfCode, string $paramLabel, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        $size = strtolower($context['taille_entreprise'] ?? '');

        $adjustment = 0.0;

        // Ajustements spécifiques par DF et paramètre
        switch ($dfCode) {
            case 'DF1': // Enterprise Strategy
                if (strpos($paramLabel, 'Croissance') !== false && strpos($constraints, 'croissance') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Innovation') !== false && strpos($constraints, 'innovation') !== false) {
                    $adjustment += 0.8;
                }
                if (strpos($paramLabel, 'Coût') !== false && strpos($constraints, 'budget') !== false) {
                    $adjustment += 1.2;
                }
                break;

            case 'DF2': // Enterprise Goals
                if (strpos($paramLabel, 'Conformité') !== false && strpos($constraints, 'conformité') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Risques') !== false && strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.8;
                }
                break;

            case 'DF3': // Risk Profile
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.5; // Augmenter tous les paramètres de risque
                }
                break;

            case 'DF5': // Threat Landscape
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'cyber') !== false) {
                    $adjustment += 0.3; // Menaces plus élevées
                }
                break;

            case 'DF6': // Compliance Requirements
                if (strpos($constraints, 'conformité') !== false || strpos($constraints, 'rgpd') !== false) {
                    $adjustment += 0.4;
                }
                break;

            case 'DF8': // Sourcing Model
                if (strpos($constraints, 'équipe') !== false && strpos($constraints, 'réduite') !== false) {
                    if (strpos($paramLabel, 'externe') !== false) {
                        $adjustment += 1.0; // Plus d'externalisation si équipe réduite
                    }
                }
                break;

            case 'DF9': // Implementation Methods
                if (strpos($constraints, 'agile') !== false && strpos($paramLabel, 'agiles') !== false) {
                    $adjustment += 0.3;
                }
                if (strpos($constraints, 'devops') !== false && strpos($paramLabel, 'DevOps') !== false) {
                    $adjustment += 0.3;
                }
                break;

            case 'DF10': // Enterprise Size
                if (strpos($size, 'petite') !== false && strpos($paramLabel, 'Petite') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'moyenne') !== false && strpos($paramLabel, 'Moyenne') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'grande') !== false && strpos($paramLabel, 'Grande') !== false) {
                    $adjustment += 0.4;
                }
                break;
        }

        return $adjustment;
    }

    /**
     * Vérifier si Ollama est disponible
     */
    public function isAvailable(): bool
    {
        return $this->isAvailable;
    }
}
