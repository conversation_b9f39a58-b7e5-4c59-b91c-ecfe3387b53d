<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaCobitAnalysisService
{
    private string $host;
    private string $model;
    private bool $isAvailable;

    public function __construct()
    {
        $this->host = env('OLLAMA_HOST', 'http://localhost:11434');
        $this->model = env('OLLAMA_COBIT_MODEL', 'cobit-auditeur');
        $this->isAvailable = $this->checkAvailability();
    }

    /**
     * Vérifier la disponibilité d'Ollama
     */
    private function checkAvailability(): bool
    {
        try {
            $response = Http::timeout(3)->get("{$this->host}/api/tags");
            return $response->successful();
        } catch (\Exception $e) {
            Log::warning('Ollama COBIT non disponible: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Analyser un document pour les Design Factors COBIT 2019
     */
    public function analyzeDocumentForDesignFactors(string $content, string $documentType, array $projectContext = []): array
    {
        if (!$this->isAvailable) {
            return $this->getFallbackAnalysis($content, $projectContext);
        }

        try {
            $prompt = $this->buildOptimizedPrompt($content, $documentType, $projectContext);
            $response = $this->callOllamaOptimized($prompt);
            
            if ($response) {
                return $this->parseAndValidateResponse($response, $projectContext);
            }
        } catch (\Exception $e) {
            Log::error('Erreur Ollama COBIT Analysis: ' . $e->getMessage());
        }

        return $this->getFallbackAnalysis($content, $projectContext);
    }

    /**
     * Construire un prompt optimisé pour l'analyse COBIT
     */
    private function buildOptimizedPrompt(string $content, string $documentType, array $context): string
    {
        // Limiter le contenu pour la rapidité
        $limitedContent = substr($content, 0, 2000);
        
        // Contexte du projet
        $companySize = $context['taille_entreprise'] ?? 'moyenne';
        $constraints = $context['contraintes'] ?? '';
        $sector = $this->detectSector($content);

        return "Expert COBIT 2019: Analyse ce document {$documentType} pour une entreprise {$companySize} du secteur {$sector}.

CONTEXTE PROJET:
- Taille: {$companySize}
- Contraintes: {$constraints}
- Secteur: {$sector}

DOCUMENT:
{$limitedContent}

MISSION: Évalue les 10 Design Factors COBIT 2019 avec des scores précis et personnalisés pour CE projet spécifique.

RÉPONSE JSON OBLIGATOIRE:
{
  \"df_scores\": {
    \"DF1\": {\"score\": 3.2, \"confidence\": 0.85, \"reasoning\": \"Stratégie claire mais manque d'alignement IT\"},
    \"DF2\": {\"score\": 4.1, \"confidence\": 0.90, \"reasoning\": \"Objectifs mesurables bien définis\"},
    \"DF3\": {\"score\": 2.8, \"confidence\": 0.75, \"reasoning\": \"Profil de risque conservateur identifié\"},
    \"DF4\": {\"score\": 3.5, \"confidence\": 0.80, \"reasoning\": \"Enjeux IT modernes présents\"},
    \"DF5\": {\"score\": 3.0, \"confidence\": 0.70, \"reasoning\": \"Menaces standard du secteur\"},
    \"DF6\": {\"score\": 4.0, \"confidence\": 0.85, \"reasoning\": \"Conformité réglementaire forte\"},
    \"DF7\": {\"score\": 3.3, \"confidence\": 0.75, \"reasoning\": \"IT en support stratégique\"},
    \"DF8\": {\"score\": 2.9, \"confidence\": 0.70, \"reasoning\": \"Sourcing mixte identifié\"},
    \"DF9\": {\"score\": 3.4, \"confidence\": 0.80, \"reasoning\": \"Méthodes agiles partielles\"},
    \"DF10\": {\"score\": 3.1, \"confidence\": 0.85, \"reasoning\": \"Taille {$companySize} confirmée\"}
  },
  \"project_specifics\": {
    \"sector_impact\": \"Secteur {$sector} influence DF6 et DF3\",
    \"size_impact\": \"Taille {$companySize} affecte DF10 et DF8\",
    \"unique_factors\": [\"Contrainte budgétaire\", \"Transformation digitale\"]
  },
  \"maturity_estimate\": 3.2,
  \"confidence_global\": 0.81
}";
    }

    /**
     * Appeler Ollama avec optimisations de performance
     */
    private function callOllamaOptimized(string $prompt): ?string
    {
        try {
            $response = Http::timeout(15)->post("{$this->host}/api/generate", [
                'model' => $this->model,
                'prompt' => $prompt,
                'stream' => false,
                'options' => [
                    'temperature' => 0.2,  // Très déterministe
                    'top_p' => 0.8,
                    'num_predict' => 800,  // Limité pour la rapidité
                    'stop' => ['}'],       // Arrêt après le JSON
                ]
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['response'] ?? null;
            }
        } catch (\Exception $e) {
            Log::error('Erreur appel Ollama optimisé: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Parser et valider la réponse Ollama
     */
    private function parseAndValidateResponse(string $response, array $context): array
    {
        // Extraire le JSON de la réponse
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonStr, true);
            
            if ($parsed && isset($parsed['df_scores'])) {
                return $this->enhanceWithProjectSpecifics($parsed, $context);
            }
        }

        // Fallback si parsing échoue
        return $this->getFallbackAnalysis('', $context);
    }

    /**
     * Améliorer l'analyse avec les spécificités du projet
     */
    private function enhanceWithProjectSpecifics(array $analysis, array $context): array
    {
        $enhanced = [
            'df_values' => [],
            'maturity_level' => $analysis['maturity_estimate'] ?? 3.0,
            'confidence' => $analysis['confidence_global'] ?? 0.8,
            'project_specifics' => $analysis['project_specifics'] ?? [],
            'ollama_enhanced' => true,
            'analysis_method' => 'Ollama COBIT Expert',
            'personalization' => $this->generatePersonalization($context)
        ];

        // Convertir les scores DF en valeurs pour les paramètres spécifiques de chaque DF
        foreach ($analysis['df_scores'] as $df => $data) {
            $score = max(1, min(5, $data['score']));
            $confidence = $data['confidence'] ?? 0.8;
            $reasoning = $data['reasoning'] ?? '';

            $enhanced['df_values'][$df] = $this->generateDFParameterValues(
                $df,
                $score,
                $confidence,
                $context
            );
        }

        return $enhanced;
    }

    /**
     * Générer des valeurs pour les paramètres d'évaluation d'un DF spécifique
     */
    private function generateContextualObjectiveValues(float $baseScore, float $confidence, string $reasoning, array $context): array
    {
        // Cette méthode sera appelée pour chaque DF individuellement
        // Elle doit retourner les valeurs pour les paramètres spécifiques du DF
        return [];
    }

    /**
     * Générer des valeurs pour les paramètres d'un DF spécifique
     */
    private function generateDFParameterValues(string $dfCode, float $baseScore, float $confidence, array $context): array
    {
        // Récupérer la structure des paramètres pour ce DF
        $dfStructure = $this->getDFStructure($dfCode);
        $paramCount = count($dfStructure['labels']);

        $values = [];
        $companySize = $context['taille_entreprise'] ?? 'moyenne';

        // Facteurs d'ajustement selon le contexte
        $sizeMultiplier = $this->getSizeMultiplier($companySize);
        $variationRange = (1 - $confidence) * 0.8; // Variation contrôlée

        for ($i = 0; $i < $paramCount; $i++) {
            $paramLabel = $dfStructure['labels'][$i];
            $paramDefault = $dfStructure['defaults'][$i];
            $paramRange = $dfStructure['metadata'];

            // Ajustement contextuel selon le paramètre
            $contextAdjustment = $this->getParameterContextAdjustment($dfCode, $paramLabel, $context);

            // Variation aléatoire contrôlée
            $randomVariation = (rand(-100, 100) / 100) * $variationRange;

            // Score final avec tous les ajustements
            $finalScore = $baseScore + $contextAdjustment + $randomVariation;
            $finalScore *= $sizeMultiplier;

            // Respecter les limites du paramètre
            $minValue = $paramRange['min'] ?? 0;
            $maxValue = $paramRange['max'] ?? 5;

            $values[] = max($minValue, min($maxValue, round($finalScore, 1)));
        }

        return $values;
    }

    /**
     * Obtenir le multiplicateur selon la taille de l'entreprise
     */
    private function getSizeMultiplier(string $size): float
    {
        return match(strtolower($size)) {
            'petite entreprise (< 100 employés)' => 0.9,
            'moyenne entreprise (100-500 employés)' => 1.0,
            'grande entreprise (500-5000 employés)' => 1.1,
            'très grande entreprise (> 5000 employés)' => 1.2,
            default => 1.0
        };
    }

    /**
     * Détecter le secteur d'activité
     */
    private function detectSector(string $content): string
    {
        $contentLower = strtolower($content);
        
        if (strpos($contentLower, 'banque') !== false || strpos($contentLower, 'finance') !== false) {
            return 'financier';
        } elseif (strpos($contentLower, 'santé') !== false || strpos($contentLower, 'médical') !== false) {
            return 'santé';
        } elseif (strpos($contentLower, 'éducation') !== false || strpos($contentLower, 'université') !== false) {
            return 'éducation';
        } elseif (strpos($contentLower, 'industrie') !== false || strpos($contentLower, 'manufacture') !== false) {
            return 'industriel';
        } elseif (strpos($contentLower, 'service') !== false || strpos($contentLower, 'conseil') !== false) {
            return 'services';
        }
        
        return 'général';
    }

    /**
     * Obtenir le type d'objectif (EDM, APO, BAI, DSS, MEA)
     */
    private function getObjectiveType(int $index): string
    {
        if ($index < 5) return 'EDM';
        if ($index < 19) return 'APO';
        if ($index < 30) return 'BAI';
        if ($index < 36) return 'DSS';
        return 'MEA';
    }

    /**
     * Ajustement selon le type d'objectif et le contexte
     */
    private function getTypeAdjustment(string $type, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        
        // Ajustements contextuels
        if (strpos($constraints, 'budget') !== false) {
            return match($type) {
                'EDM' => 0.2,  // Gouvernance importante avec budget limité
                'APO' => 0.1,  // Planification cruciale
                'BAI' => -0.3, // Moins de nouveaux projets
                'DSS' => 0.0,  // Maintien des services
                'MEA' => 0.1,  // Mesure importante
                default => 0.0
            };
        }
        
        if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'rgpd') !== false) {
            return match($type) {
                'EDM' => 0.3,  // Gouvernance sécurité
                'APO' => 0.2,  // Planification sécurité
                'BAI' => 0.1,  // Implémentation sécurisée
                'DSS' => 0.4,  // Services sécurisés
                'MEA' => 0.2,  // Monitoring sécurité
                default => 0.0
            };
        }
        
        return 0.0;
    }

    /**
     * Générer la personnalisation du projet
     */
    private function generatePersonalization(array $context): array
    {
        return [
            'company_size' => $context['taille_entreprise'] ?? 'non spécifiée',
            'constraints_impact' => $this->analyzeConstraintsImpact($context['contraintes'] ?? ''),
            'sector_considerations' => 'Ajustements sectoriels appliqués',
            'customization_level' => 'Élevé - Analyse personnalisée'
        ];
    }

    /**
     * Analyser l'impact des contraintes
     */
    private function analyzeConstraintsImpact(string $constraints): array
    {
        $impacts = [];
        $constraintsLower = strtolower($constraints);
        
        if (strpos($constraintsLower, 'budget') !== false) {
            $impacts[] = 'Optimisation des coûts prioritaire';
        }
        if (strpos($constraintsLower, 'temps') !== false) {
            $impacts[] = 'Implémentation accélérée requise';
        }
        if (strpos($constraintsLower, 'sécurité') !== false) {
            $impacts[] = 'Renforcement sécurité critique';
        }
        if (strpos($constraintsLower, 'conformité') !== false) {
            $impacts[] = 'Conformité réglementaire prioritaire';
        }
        
        return $impacts ?: ['Aucune contrainte spécifique identifiée'];
    }

    /**
     * Analyse de fallback si Ollama indisponible
     */
    private function getFallbackAnalysis(string $content, array $context): array
    {
        Log::info('Utilisation de l\'analyse de fallback COBIT (Ollama indisponible)');
        
        // Analyse basique mais contextualisée
        $baseScores = $this->calculateBasicScores($content);
        $sizeMultiplier = $this->getSizeMultiplier($context['taille_entreprise'] ?? 'moyenne');
        
        $dfValues = [];
        for ($i = 1; $i <= 10; $i++) {
            $dfCode = "DF{$i}";
            $baseScore = $baseScores[$dfCode] ?? 3.0;
            $adjustedScore = $baseScore * $sizeMultiplier;
            $dfValues[$dfCode] = $this->generateDFParameterValues($dfCode, $adjustedScore, 0.6, $context);
        }

        return [
            'df_values' => $dfValues,
            'maturity_level' => array_sum($baseScores) / count($baseScores),
            'confidence' => 0.6,
            'project_specifics' => ['Analyse de base avec personnalisation'],
            'ollama_enhanced' => false,
            'analysis_method' => 'Analyse de base contextualisée',
            'personalization' => $this->generatePersonalization($context)
        ];
    }

    /**
     * Calcul des scores de base
     */
    private function calculateBasicScores(string $content): array
    {
        $contentLower = strtolower($content);
        
        $dfKeywords = [
            'DF1' => ['stratégie', 'objectifs', 'vision'],
            'DF2' => ['kpi', 'métriques', 'performance'],
            'DF3' => ['risque', 'sécurité', 'menace'],
            'DF4' => ['enjeux', 'défis', 'problèmes'],
            'DF5' => ['menaces', 'cyberattaque', 'vulnérabilité'],
            'DF6' => ['conformité', 'réglementation', 'audit'],
            'DF7' => ['rôle', 'positionnement', 'fonction'],
            'DF8' => ['sourcing', 'externalisation', 'fournisseur'],
            'DF9' => ['méthodes', 'agile', 'implémentation'],
            'DF10' => ['taille', 'complexité', 'envergure']
        ];

        $scores = [];
        foreach ($dfKeywords as $df => $keywords) {
            $score = 2.0; // Score de base
            foreach ($keywords as $keyword) {
                $score += substr_count($contentLower, $keyword) * 0.3;
            }
            $scores[$df] = min(5, max(1, $score));
        }

        return $scores;
    }

    /**
     * Obtenir la structure d'un Design Factor
     */
    private function getDFStructure(string $dfCode): array
    {
        // Structure des Design Factors selon le seeder
        $dfStructures = [
            'DF1' => [
                'labels' => ['Croissance', 'Stabilité', 'Coût', 'Innovation'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF2' => [
                'labels' => ['Portefeuille agile', 'Risques métier', 'Conformité réglementaire', 'Objectif 4'],
                'defaults' => [1, 1, 1, 1],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF3' => [
                'labels' => ['Investissement IT', 'Gestion programmes', 'Coûts IT', 'Expertise IT'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF4' => [
                'labels' => ['Problème IT 1', 'Problème IT 2', 'Problème IT 3', 'Problème IT 4'],
                'defaults' => [3, 3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 5, 'step' => 1]
            ],
            'DF5' => [
                'labels' => ['Menaces externes', 'Menaces internes'],
                'defaults' => [0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF6' => [
                'labels' => ['Exigences réglementaires', 'Exigences sectorielles', 'Exigences internes'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF7' => [
                'labels' => ['Support', 'Factory', 'Turnaround'],
                'defaults' => [3, 3, 3],
                'metadata' => ['min' => 1, 'max' => 4, 'step' => 1]
            ],
            'DF8' => [
                'labels' => ['Modèle interne', 'Modèle externe'],
                'defaults' => [1, 1],
                'metadata' => ['min' => 1, 'max' => 3, 'step' => 1]
            ],
            'DF9' => [
                'labels' => ['Méthodes agiles', 'DevOps', 'Méthodes traditionnelles'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ],
            'DF10' => [
                'labels' => ['Petite entreprise', 'Moyenne entreprise', 'Grande entreprise'],
                'defaults' => [0.5, 0.5, 0.5],
                'metadata' => ['min' => 0, 'max' => 1, 'step' => 0.1]
            ]
        ];

        return $dfStructures[$dfCode] ?? $dfStructures['DF1'];
    }

    /**
     * Ajustement contextuel pour un paramètre spécifique
     */
    private function getParameterContextAdjustment(string $dfCode, string $paramLabel, array $context): float
    {
        $constraints = strtolower($context['contraintes'] ?? '');
        $size = strtolower($context['taille_entreprise'] ?? '');

        $adjustment = 0.0;

        // Ajustements spécifiques par DF et paramètre
        switch ($dfCode) {
            case 'DF1': // Enterprise Strategy
                if (strpos($paramLabel, 'Croissance') !== false && strpos($constraints, 'croissance') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Innovation') !== false && strpos($constraints, 'innovation') !== false) {
                    $adjustment += 0.8;
                }
                if (strpos($paramLabel, 'Coût') !== false && strpos($constraints, 'budget') !== false) {
                    $adjustment += 1.2;
                }
                break;

            case 'DF2': // Enterprise Goals
                if (strpos($paramLabel, 'Conformité') !== false && strpos($constraints, 'conformité') !== false) {
                    $adjustment += 1.0;
                }
                if (strpos($paramLabel, 'Risques') !== false && strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.8;
                }
                break;

            case 'DF3': // Risk Profile
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'risque') !== false) {
                    $adjustment += 0.5; // Augmenter tous les paramètres de risque
                }
                break;

            case 'DF5': // Threat Landscape
                if (strpos($constraints, 'sécurité') !== false || strpos($constraints, 'cyber') !== false) {
                    $adjustment += 0.3; // Menaces plus élevées
                }
                break;

            case 'DF6': // Compliance Requirements
                if (strpos($constraints, 'conformité') !== false || strpos($constraints, 'rgpd') !== false) {
                    $adjustment += 0.4;
                }
                break;

            case 'DF8': // Sourcing Model
                if (strpos($constraints, 'équipe') !== false && strpos($constraints, 'réduite') !== false) {
                    if (strpos($paramLabel, 'externe') !== false) {
                        $adjustment += 1.0; // Plus d'externalisation si équipe réduite
                    }
                }
                break;

            case 'DF9': // Implementation Methods
                if (strpos($constraints, 'agile') !== false && strpos($paramLabel, 'agiles') !== false) {
                    $adjustment += 0.3;
                }
                if (strpos($constraints, 'devops') !== false && strpos($paramLabel, 'DevOps') !== false) {
                    $adjustment += 0.3;
                }
                break;

            case 'DF10': // Enterprise Size
                if (strpos($size, 'petite') !== false && strpos($paramLabel, 'Petite') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'moyenne') !== false && strpos($paramLabel, 'Moyenne') !== false) {
                    $adjustment += 0.4;
                } elseif (strpos($size, 'grande') !== false && strpos($paramLabel, 'Grande') !== false) {
                    $adjustment += 0.4;
                }
                break;
        }

        return $adjustment;
    }

    /**
     * Vérifier si Ollama est disponible
     */
    public function isAvailable(): bool
    {
        return $this->isAvailable;
    }
}
