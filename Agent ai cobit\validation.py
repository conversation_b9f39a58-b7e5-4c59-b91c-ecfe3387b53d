"""
Module de validation et gestion d'erreurs pour l'Agent IA d'Audit Gouvernance IT
"""

import os
# import magic  # Commenté car non disponible par défaut
import logging
from typing import Tuple, Optional, Dict, Any
from pathlib import Path
import mimetypes

# Configuration du logging
logger = logging.getLogger(__name__)

# Configuration des limites
TAILLE_MAX_FICHIER = 16 * 1024 * 1024  # 16MB
EXTENSIONS_AUTORISEES = {'.pdf', '.xlsx', '.xls'}
TYPES_MIME_AUTORISES = {
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
}

class ValidationError(Exception):
    """Exception personnalisée pour les erreurs de validation."""
    pass

class FileValidationError(ValidationError):
    """Exception pour les erreurs de validation de fichiers."""
    pass

class ContentValidationError(ValidationError):
    """Exception pour les erreurs de validation de contenu."""
    pass

def valider_fichier(chemin_fichier: str) -> <PERSON><PERSON>[bool, Optional[str]]:
    """
    Valide un fichier uploadé selon plusieurs critères.
    
    Args:
        chemin_fichier: Chemin vers le fichier à valider
        
    Returns:
        Tuple[bool, Optional[str]]: (est_valide, message_erreur)
    """
    try:
        # Vérifier l'existence du fichier
        if not os.path.exists(chemin_fichier):
            return False, "Le fichier n'existe pas"
        
        # Vérifier la taille du fichier
        taille_fichier = os.path.getsize(chemin_fichier)
        if taille_fichier == 0:
            return False, "Le fichier est vide"
        
        if taille_fichier > TAILLE_MAX_FICHIER:
            taille_mb = taille_fichier / (1024 * 1024)
            return False, f"Fichier trop volumineux ({taille_mb:.1f}MB). Maximum autorisé: 16MB"
        
        # Vérifier l'extension
        extension = Path(chemin_fichier).suffix.lower()
        if extension not in EXTENSIONS_AUTORISEES:
            return False, f"Extension non autorisée: {extension}. Extensions acceptées: {', '.join(EXTENSIONS_AUTORISEES)}"
        
        # Vérifier le type MIME
        type_mime, _ = mimetypes.guess_type(chemin_fichier)
        if type_mime not in TYPES_MIME_AUTORISES:
            return False, f"Type de fichier non autorisé: {type_mime}"
        
        # Validation spécifique par type de fichier
        if extension == '.pdf':
            return valider_pdf(chemin_fichier)
        elif extension in ['.xlsx', '.xls']:
            return valider_excel(chemin_fichier)
        
        return True, None
        
    except Exception as e:
        logger.error(f"Erreur lors de la validation du fichier {chemin_fichier}: {str(e)}")
        return False, f"Erreur de validation: {str(e)}"

def valider_pdf(chemin_fichier: str) -> Tuple[bool, Optional[str]]:
    """Valide spécifiquement un fichier PDF."""
    try:
        try:
            import fitz  # PyMuPDF
        except ImportError:
            return False, "PyMuPDF non disponible - impossible de valider les fichiers PDF"

        with fitz.open(chemin_fichier) as doc:
            # Vérifier que le PDF n'est pas corrompu
            if doc.page_count == 0:
                return False, "Le PDF ne contient aucune page"

            # Vérifier qu'il y a du contenu textuel
            contenu_total = ""
            for page in doc:
                contenu_total += page.get_text()

            if len(contenu_total.strip()) < 50:
                return False, "Le PDF ne contient pas suffisamment de texte analysable"

            # Vérifier les métadonnées
            if doc.metadata.get('encrypted', False):
                return False, "Le PDF est protégé par mot de passe"

        return True, None

    except Exception as e:
        logger.error(f"Erreur lors de la validation PDF: {str(e)}")
        return False, f"Fichier PDF corrompu ou illisible: {str(e)}"

def valider_excel(chemin_fichier: str) -> Tuple[bool, Optional[str]]:
    """Valide spécifiquement un fichier Excel."""
    try:
        import pandas as pd
        
        # Essayer de lire le fichier Excel
        excel_file = pd.ExcelFile(chemin_fichier)
        
        if len(excel_file.sheet_names) == 0:
            return False, "Le fichier Excel ne contient aucune feuille"
        
        # Vérifier qu'il y a des données
        donnees_trouvees = False
        for sheet_name in excel_file.sheet_names:
            try:
                df = pd.read_excel(chemin_fichier, sheet_name=sheet_name)
                if not df.empty and len(df.columns) > 0:
                    donnees_trouvees = True
                    break
            except Exception:
                continue
        
        if not donnees_trouvees:
            return False, "Le fichier Excel ne contient pas de données analysables"
        
        return True, None
        
    except Exception as e:
        logger.error(f"Erreur lors de la validation Excel: {str(e)}")
        return False, f"Fichier Excel corrompu ou illisible: {str(e)}"

def valider_contenu_analyse(contenu: str) -> Tuple[bool, Optional[str]]:
    """Valide le contenu extrait pour l'analyse."""
    if not contenu or len(contenu.strip()) < 100:
        return False, "Contenu insuffisant pour l'analyse (minimum 100 caractères)"
    
    # Vérifier qu'il y a des mots significatifs
    mots = contenu.split()
    if len(mots) < 20:
        return False, "Contenu insuffisant pour l'analyse (minimum 20 mots)"
    
    # Vérifier la présence de caractères non-ASCII (peut indiquer un problème d'encodage)
    try:
        contenu.encode('utf-8')
    except UnicodeEncodeError:
        return False, "Problème d'encodage du contenu"
    
    return True, None

def nettoyer_nom_fichier(nom_fichier: str) -> str:
    """Nettoie et sécurise un nom de fichier."""
    # Supprimer les caractères dangereux
    caracteres_interdits = '<>:"/\\|?*'
    nom_nettoye = ''.join(c for c in nom_fichier if c not in caracteres_interdits)
    
    # Limiter la longueur
    if len(nom_nettoye) > 100:
        nom_base, extension = os.path.splitext(nom_nettoye)
        nom_nettoye = nom_base[:95] + extension
    
    # Éviter les noms réservés Windows
    noms_reserves = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
    nom_base = os.path.splitext(nom_nettoye)[0].upper()
    if nom_base in noms_reserves:
        nom_nettoye = f"fichier_{nom_nettoye}"
    
    return nom_nettoye

def valider_configuration_email(email: str) -> Tuple[bool, Optional[str]]:
    """Valide une adresse email."""
    import re
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        return False, "Format d'email invalide"
    
    if len(email) > 254:
        return False, "Adresse email trop longue"
    
    return True, None

def gerer_erreur_traitement(erreur: Exception, contexte: str) -> Dict[str, Any]:
    """Gère et formate les erreurs de traitement."""
    logger.error(f"Erreur dans {contexte}: {str(erreur)}")
    
    # Catégoriser l'erreur
    if isinstance(erreur, FileNotFoundError):
        categorie = "fichier_introuvable"
        message_utilisateur = "Le fichier spécifié n'a pas été trouvé"
    elif isinstance(erreur, PermissionError):
        categorie = "permission_refusee"
        message_utilisateur = "Permissions insuffisantes pour accéder au fichier"
    elif isinstance(erreur, MemoryError):
        categorie = "memoire_insuffisante"
        message_utilisateur = "Fichier trop volumineux pour être traité"
    elif isinstance(erreur, ValidationError):
        categorie = "validation"
        message_utilisateur = str(erreur)
    else:
        categorie = "erreur_inconnue"
        message_utilisateur = "Une erreur inattendue s'est produite"
    
    return {
        "categorie": categorie,
        "message_utilisateur": message_utilisateur,
        "message_technique": str(erreur),
        "contexte": contexte
    }

def creer_rapport_erreur(erreur_info: Dict[str, Any]) -> str:
    """Crée un rapport d'erreur formaté pour l'utilisateur."""
    return f"""
❌ ERREUR LORS DU TRAITEMENT

🔍 Contexte: {erreur_info['contexte']}
📝 Description: {erreur_info['message_utilisateur']}
🏷️ Type: {erreur_info['categorie']}

💡 SUGGESTIONS:
- Vérifiez que votre fichier n'est pas corrompu
- Assurez-vous que le fichier contient du texte analysable
- Essayez avec un fichier plus petit si nécessaire
- Contactez le support si le problème persiste

🔧 Détails techniques: {erreur_info['message_technique']}
"""
