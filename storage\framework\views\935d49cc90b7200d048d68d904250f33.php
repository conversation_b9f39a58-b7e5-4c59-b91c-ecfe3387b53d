<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent AI COBIT - Gestion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        .kpmg-blue { color: #00338D; }
        .kmpg-bg { background-color: #00338D; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 15px 30px rgba(0,0,0,0.1); }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .status-running { color: #10B981; }
        .status-stopped { color: #EF4444; }
        .log-container { 
            background: #1a1a1a; 
            color: #00ff00; 
            font-family: 'Courier New', monospace; 
            max-height: 400px; 
            overflow-y: auto; 
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="kmpg-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('cobit.home')); ?>" class="text-blue-200 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Retour
                    </a>
                    <div>
                        <h1 class="text-xl font-bold">Agent AI COBIT</h1>
                        <p class="text-blue-200 text-sm">Gestion et Monitoring</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="status-indicator" class="flex items-center">
                        <div class="w-3 h-3 rounded-full mr-2" id="status-dot"></div>
                        <span id="status-text">Vérification...</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-6 py-8">
        <!-- Status Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 card-hover">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold kpmg-blue">
                    <i class="fas fa-robot mr-2"></i>Statut de l'Agent AI
                </h2>
                <button onclick="refreshStatus()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Actualiser
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl font-bold mb-2" id="status-display">
                        <i class="fas fa-question-circle text-gray-400"></i>
                    </div>
                    <p class="text-gray-600">Statut</p>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl font-bold mb-2 text-blue-600" id="uptime-display">
                        --:--:--
                    </div>
                    <p class="text-gray-600">Temps d'activité</p>
                </div>
                
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-3xl font-bold mb-2 text-green-600" id="files-processed">
                        0
                    </div>
                    <p class="text-gray-600">Fichiers traités</p>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8 card-hover">
            <h2 class="text-2xl font-bold kpmg-blue mb-6">
                <i class="fas fa-cogs mr-2"></i>Panneau de Contrôle
            </h2>
            
            <div class="flex flex-wrap gap-4">
                <button onclick="launchAgent()" id="launch-btn" class="bg-gradient-to-r from-green-600 to-teal-600 text-white px-6 py-3 rounded-lg font-bold hover:from-green-700 hover:to-teal-700 transition-all transform hover:scale-105">
                    <i class="fas fa-play mr-2"></i>Lancer l'Agent
                </button>
                
                <button onclick="stopAgent()" id="stop-btn" class="bg-gradient-to-r from-red-600 to-pink-600 text-white px-6 py-3 rounded-lg font-bold hover:from-red-700 hover:to-pink-700 transition-all transform hover:scale-105" disabled>
                    <i class="fas fa-stop mr-2"></i>Arrêter l'Agent
                </button>
                
                <button onclick="openAgentWindow()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105">
                    <i class="fas fa-external-link-alt mr-2"></i>Ouvrir Interface
                </button>
                
                <button onclick="refreshLogs()" class="bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-6 py-3 rounded-lg font-bold hover:from-yellow-700 hover:to-orange-700 transition-all transform hover:scale-105">
                    <i class="fas fa-file-alt mr-2"></i>Actualiser Logs
                </button>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold kpmg-blue">
                    <i class="fas fa-terminal mr-2"></i>Logs de l'Agent AI
                </h2>
                <div class="flex items-center space-x-2">
                    <label class="flex items-center">
                        <input type="checkbox" id="auto-refresh" class="mr-2">
                        <span class="text-sm text-gray-600">Auto-actualisation</span>
                    </label>
                </div>
            </div>
            
            <div class="log-container rounded-lg p-4 border">
                <pre id="logs-content" class="text-sm whitespace-pre-wrap">Chargement des logs...</pre>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="fixed top-4 right-4 z-50"></div>

    <script>
        let autoRefreshInterval = null;
        let statusCheckInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            refreshLogs();
            
            // Vérification du statut toutes les 10 secondes
            statusCheckInterval = setInterval(refreshStatus, 10000);
            
            // Auto-refresh des logs si activé
            document.getElementById('auto-refresh').addEventListener('change', function() {
                if (this.checked) {
                    autoRefreshInterval = setInterval(refreshLogs, 5000);
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                }
            });
        });

        // Lancer l'agent
        async function launchAgent() {
            showNotification('🚀 Lancement de l\'Agent AI...', 'info');
            
            try {
                const response = await fetch('/cobit/launch-agent', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('✅ Agent AI lancé avec succès!', 'success');
                    setTimeout(refreshStatus, 2000);
                } else {
                    showNotification('❌ Erreur: ' + data.message, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur de connexion', 'error');
                console.error('Erreur:', error);
            }
        }

        // Arrêter l'agent
        async function stopAgent() {
            showNotification('⏹️ Arrêt de l\'Agent AI...', 'info');
            
            try {
                const response = await fetch('/cobit/agent-stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('✅ Agent AI arrêté', 'success');
                    setTimeout(refreshStatus, 2000);
                } else {
                    showNotification('❌ Erreur: ' + data.message, 'error');
                }
            } catch (error) {
                showNotification('❌ Erreur de connexion', 'error');
                console.error('Erreur:', error);
            }
        }

        // Actualiser le statut
        async function refreshStatus() {
            try {
                const response = await fetch('/cobit/agent-status');
                const data = await response.json();
                
                if (data.success) {
                    updateStatusDisplay(data.running);
                } else {
                    updateStatusDisplay(false);
                }
            } catch (error) {
                console.error('Erreur lors de la vérification du statut:', error);
                updateStatusDisplay(false);
            }
        }

        // Mettre à jour l'affichage du statut
        function updateStatusDisplay(isRunning) {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            const statusDisplay = document.getElementById('status-display');
            const launchBtn = document.getElementById('launch-btn');
            const stopBtn = document.getElementById('stop-btn');
            
            if (isRunning) {
                statusDot.className = 'w-3 h-3 rounded-full mr-2 bg-green-500 pulse-animation';
                statusText.textContent = 'En cours d\'exécution';
                statusDisplay.innerHTML = '<i class="fas fa-check-circle status-running"></i>';
                launchBtn.disabled = true;
                launchBtn.classList.add('opacity-50', 'cursor-not-allowed');
                stopBtn.disabled = false;
                stopBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                statusDot.className = 'w-3 h-3 rounded-full mr-2 bg-red-500';
                statusText.textContent = 'Arrêté';
                statusDisplay.innerHTML = '<i class="fas fa-times-circle status-stopped"></i>';
                launchBtn.disabled = false;
                launchBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                stopBtn.disabled = true;
                stopBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        // Actualiser les logs
        async function refreshLogs() {
            try {
                const response = await fetch('/cobit/agent-logs');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('logs-content').textContent = data.logs || 'Aucun log disponible';
                } else {
                    document.getElementById('logs-content').textContent = 'Erreur lors du chargement des logs: ' + data.message;
                }
            } catch (error) {
                document.getElementById('logs-content').textContent = 'Erreur de connexion lors du chargement des logs';
                console.error('Erreur:', error);
            }
        }

        // Ouvrir l'interface de l'agent dans une nouvelle fenêtre
        function openAgentWindow() {
            // Remplacez par l'URL de votre agent AI si c'est une application web
            const agentUrl = 'http://localhost:8080'; // Exemple
            window.open(agentUrl, '_blank', 'width=1200,height=800');
        }

        // Afficher une notification
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const bgColor = type === 'success' ? 'bg-green-500' : 
                           type === 'error' ? 'bg-red-500' : 
                           type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';
            
            notification.className = `${bgColor} text-white p-4 rounded-lg shadow-lg mb-2 transform transition-all duration-300 translate-x-full`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Animation d'entrée
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);
            
            // Auto-suppression après 5 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, 5000);
        }

        // Nettoyage lors de la fermeture de la page
        window.addEventListener('beforeunload', function() {
            if (statusCheckInterval) clearInterval(statusCheckInterval);
            if (autoRefreshInterval) clearInterval(autoRefreshInterval);
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\symfcopitejihed\symfcopite\symf\symfcobite\cobit-laravel\resources\views/cobit/agent-ai.blade.php ENDPATH**/ ?>