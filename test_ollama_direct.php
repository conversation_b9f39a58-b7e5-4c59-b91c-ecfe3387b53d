<?php

echo "🤖 TEST DIRECT OLLAMA COBIT\n";
echo "===========================\n\n";

// Test direct de l'API Ollama
$host = 'http://localhost:11434';
$model = 'mistral';

// Contenu de test
$testContent = "ENTREPRISE: TestCorp
SECTEUR: Technologie
TAILLE: 50 employés
BUDGET: 100k€
CONTRAINTES: Budget limité, équipe réduite
OBJECTIFS: Croissance rapide, innovation, sécurité
ENJEUX: Scalabilité, agilité, cybersécurité";

$prompt = "Vous êtes un consultant COBIT 2019. Analysez ce document d'entreprise et évaluez les 10 Design Factors.

ENTREPRISE: TestCorp
TAILLE: Petite entreprise
SECTEUR: Technologie

DOCUMENT:
{$testContent}

Analysez le document et donnez des scores de 1.0 à 5.0 pour chaque Design Factor selon le contenu.

Répondez UNIQUEMENT avec ce JSON (remplacez les X.X par vos scores):

{
  \"df_scores\": {
    \"DF1\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF2\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"},
    \"DF3\": {\"score\": X.X, \"reasoning\": \"Votre analyse basée sur le document\"}
  },
  \"maturity_estimate\": X.X
}";

echo "📤 Envoi de la requête à Ollama...\n";

$data = [
    'model' => $model,
    'prompt' => $prompt,
    'stream' => false,
    'options' => [
        'temperature' => 0.3,
        'top_p' => 0.9,
        'num_predict' => 1000,
        'seed' => rand(1, 1000000)
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $host . '/api/generate');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);
$duration = round(($endTime - $startTime), 2);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "⏱️ Durée: {$duration} secondes\n";
echo "📊 Code HTTP: {$httpCode}\n";

if (curl_errno($ch)) {
    echo "❌ Erreur cURL: " . curl_error($ch) . "\n";
} elseif ($httpCode === 200) {
    echo "✅ Réponse reçue !\n\n";
    
    $responseData = json_decode($response, true);
    if ($responseData && isset($responseData['response'])) {
        $ollamaResponse = $responseData['response'];
        echo "🤖 Réponse Ollama:\n";
        echo "==================\n";
        echo $ollamaResponse . "\n\n";
        
        // Essayer de parser le JSON
        $jsonStart = strpos($ollamaResponse, '{');
        $jsonEnd = strrpos($ollamaResponse, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($ollamaResponse, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonStr, true);
            
            if ($parsed) {
                echo "✅ JSON parsé avec succès !\n";
                
                if (isset($parsed['df_scores'])) {
                    echo "📊 Design Factors trouvés: " . count($parsed['df_scores']) . "\n";
                    foreach ($parsed['df_scores'] as $df => $data) {
                        $score = $data['score'] ?? 'N/A';
                        $reasoning = substr($data['reasoning'] ?? 'N/A', 0, 50) . '...';
                        echo "   - {$df}: {$score}/5 - {$reasoning}\n";
                    }
                }
                
                if (isset($parsed['maturity_estimate'])) {
                    echo "🎯 Maturité estimée: " . $parsed['maturity_estimate'] . "/5\n";
                }
            } else {
                echo "❌ Erreur parsing JSON: " . json_last_error_msg() . "\n";
                echo "📄 JSON brut: " . substr($jsonStr, 0, 200) . "...\n";
            }
        } else {
            echo "⚠️ Aucun JSON trouvé dans la réponse\n";
        }
    } else {
        echo "❌ Format de réponse invalide\n";
        echo "📄 Réponse brute: " . substr($response, 0, 500) . "...\n";
    }
} else {
    echo "❌ Erreur HTTP {$httpCode}\n";
    echo "📄 Réponse: " . substr($response, 0, 500) . "...\n";
}

curl_close($ch);

echo "\n🎯 RÉSUMÉ DU TEST\n";
echo "=================\n";

if ($httpCode === 200) {
    echo "✅ Ollama est opérationnel\n";
    echo "✅ Modèle 'cobit-auditeur' disponible\n";
    echo "✅ Analyse personnalisée possible\n";
    echo "⚡ Performance: {$duration}s\n";
} else {
    echo "❌ Problème avec Ollama\n";
    echo "❌ Vérifiez que Ollama est démarré\n";
    echo "❌ Vérifiez que le modèle 'cobit-auditeur' est installé\n";
}

echo "\n💡 PROCHAINES ÉTAPES:\n";
echo "1. Si Ollama fonctionne, testez via l'interface web\n";
echo "2. Uploadez un document et vérifiez l'analyse personnalisée\n";
echo "3. Comparez les résultats avec différents documents\n";
echo "4. Validez que les scores varient selon le contenu\n";

echo "\n🚀 Test terminé !\n";
