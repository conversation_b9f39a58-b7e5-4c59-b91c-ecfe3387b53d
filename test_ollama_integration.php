<?php

require_once 'vendor/autoload.php';

use App\Services\OllamaCobitService;

echo "🤖 Test d'intégration Ollama COBIT\n";
echo "================================\n\n";

// Test 1: Vérifier la disponibilité d'Ollama
echo "1. Test de disponibilité Ollama...\n";
$ollamaService = new OllamaCobitService();

if ($ollamaService->isAvailable()) {
    echo "✅ Ollama est disponible et accessible\n";
    
    $modelInfo = $ollamaService->getModelInfo();
    echo "   Modèle: {$modelInfo['model']}\n";
    echo "   URL: {$modelInfo['base_url']}\n\n";
    
    // Test 2: Analyse d'un document de test
    echo "2. Test d'analyse de document...\n";
    
    $testContent = "
    Notre entreprise développe une stratégie IT alignée sur les objectifs métier.
    Le comité de gouvernance IT supervise les décisions technologiques.
    L'analyse des risques IT est effectuée trimestriellement.
    Le budget IT représente 15% du chiffre d'affaires.
    Les clients sont impliqués dans la définition des besoins.
    Le personnel IT bénéficie de formations COBIT 2019.
    Les processus IT suivent le référentiel COBIT 2019.
    L'infrastructure IT est modernisée progressivement.
    L'organisation gère 500 utilisateurs sur 3 sites.
    La conformité RGPD est assurée par des contrôles réguliers.
    ";
    
    echo "   Analyse en cours avec Ollama...\n";
    $result = $ollamaService->analyzeDocumentForCobit($testContent, 'test');
    
    if ($result && isset($result['df_values'])) {
        echo "✅ Analyse réussie !\n";
        echo "   Design Factors analysés: " . count($result['df_values']) . "\n";
        echo "   Niveau de maturité: " . $result['maturity_level'] . "/5\n";
        echo "   Confiance: " . round($result['confidence'] * 100) . "%\n";
        echo "   Ollama utilisé: " . ($result['ollama_enhanced'] ? 'Oui' : 'Non') . "\n";
        
        if (!empty($result['key_findings'])) {
            echo "   Points clés identifiés: " . count($result['key_findings']) . "\n";
        }
        
        if (!empty($result['recommendations'])) {
            echo "   Recommandations: " . count($result['recommendations']) . "\n";
        }
        
        echo "\n   Exemple de scores DF:\n";
        foreach (array_slice($result['df_values'], 0, 3) as $df => $values) {
            $avgScore = round(array_sum($values) / count($values), 1);
            echo "   - {$df}: Score moyen {$avgScore}/5\n";
        }
        
    } else {
        echo "❌ Échec de l'analyse\n";
    }
    
} else {
    echo "❌ Ollama n'est pas disponible\n";
    echo "   Vérifiez qu'Ollama est démarré: ollama serve\n";
    echo "   Vérifiez que le modèle cobit-auditeur est installé\n";
}

echo "\n3. Test du fallback (analyse de base)...\n";
// Forcer l'utilisation du fallback pour comparaison
$fallbackResult = (new OllamaCobitService())->analyzeDocumentForCobit("test minimal", 'test');

if ($fallbackResult) {
    echo "✅ Système de fallback fonctionnel\n";
    echo "   Maturité estimée: " . $fallbackResult['maturity_level'] . "/5\n";
    echo "   Confiance: " . round($fallbackResult['confidence'] * 100) . "%\n";
} else {
    echo "❌ Problème avec le système de fallback\n";
}

echo "\n🎯 Test terminé !\n";
echo "================================\n";
echo "Votre Agent IA COBIT avec Ollama est " . ($ollamaService->isAvailable() ? "OPÉRATIONNEL" : "en mode FALLBACK") . "\n";
