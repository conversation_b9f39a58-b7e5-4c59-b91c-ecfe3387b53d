#!/usr/bin/env python3
"""
Script de démarrage simple pour l'Agent IA d'Audit Gouvernance IT
"""

import os
import sys
import subprocess
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verifier_dependances_minimales():
    """Vérifie les dépendances minimales."""
    dependances_requises = ['flask', 'pandas', 'nltk']
    dependances_manquantes = []
    
    for dep in dependances_requises:
        try:
            __import__(dep)
            logger.info(f"✅ {dep} disponible")
        except ImportError:
            dependances_manquantes.append(dep)
            logger.warning(f"❌ {dep} manquant")
    
    return dependances_manquantes

def installer_dependances_manquantes(dependances):
    """Installe les dépendances manquantes."""
    if not dependances:
        return True
    
    logger.info(f"Installation des dépendances manquantes: {', '.join(dependances)}")
    
    for dep in dependances:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            logger.info(f"✅ {dep} installé avec succès")
        except subprocess.CalledProcessError:
            logger.error(f"❌ Échec de l'installation de {dep}")
            return False
    
    return True

def configurer_nltk():
    """Configure NLTK avec gestion d'erreurs."""
    try:
        import nltk
        logger.info("Configuration de NLTK...")
        
        # Télécharger les ressources avec gestion d'erreurs
        try:
            nltk.download('punkt', quiet=True)
            logger.info("✅ NLTK punkt téléchargé")
        except:
            logger.warning("⚠️ Impossible de télécharger punkt")
        
        try:
            nltk.download('stopwords', quiet=True)
            logger.info("✅ NLTK stopwords téléchargé")
        except:
            logger.warning("⚠️ Impossible de télécharger stopwords")
        
        return True
    except ImportError:
        logger.error("❌ NLTK non disponible")
        return False

def creer_dossiers():
    """Crée les dossiers nécessaires."""
    dossiers = ['uploads', 'reports', 'logs']
    for dossier in dossiers:
        if not os.path.exists(dossier):
            os.makedirs(dossier)
            logger.info(f"📁 Dossier créé: {dossier}")
        else:
            logger.info(f"📁 Dossier existant: {dossier}")

def verifier_ollama():
    """Vérifie si Ollama est disponible."""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            logger.info("✅ Ollama est disponible")
            if 'cobit-auditeur' in result.stdout:
                logger.info("✅ Modèle 'cobit-auditeur' trouvé")
            else:
                logger.warning("⚠️ Modèle 'cobit-auditeur' non trouvé")
                logger.info("💡 Créez le modèle avec: ollama create cobit-auditeur -f Modelfile")
            return True
        else:
            logger.warning("⚠️ Ollama non accessible")
            return False
    except (FileNotFoundError, subprocess.TimeoutExpired):
        logger.warning("⚠️ Ollama non installé ou non démarré")
        logger.info("💡 Installez Ollama depuis https://ollama.ai")
        logger.info("💡 Puis démarrez-le avec: ollama serve")
        return False

def demarrer_application():
    """Démarre l'application Flask."""
    try:
        logger.info("🚀 Démarrage de l'Agent IA d'Audit Gouvernance IT...")
        logger.info("🌐 L'application sera disponible sur: http://localhost:5000")
        logger.info("📧 Email de destination: <EMAIL>")
        logger.info("⏹️ Appuyez sur Ctrl+C pour arrêter")
        
        # Importer et démarrer l'application
        from app import app
        app.run(debug=True, port=5000, host='0.0.0.0')
        
    except ImportError as e:
        logger.error(f"❌ Erreur d'import: {e}")
        logger.error("💡 Vérifiez que tous les fichiers sont présents")
        return False
    except Exception as e:
        logger.error(f"❌ Erreur lors du démarrage: {e}")
        return False

def afficher_aide():
    """Affiche l'aide et les instructions."""
    print("""
🤖 AGENT IA D'AUDIT GOUVERNANCE IT - COBIT 2019
===============================================

📋 INSTRUCTIONS DE DÉMARRAGE:

1. 🔧 PRÉREQUIS:
   - Python 3.8+ installé
   - Connexion internet pour l'installation des dépendances
   - Ollama installé et démarré (optionnel pour les tests)

2. 🚀 DÉMARRAGE RAPIDE:
   python start.py

3. 📁 UTILISATION:
   - Ouvrez http://localhost:5000 dans votre navigateur
   - Sélectionnez un fichier PDF ou Excel
   - Cliquez sur "Lancer l'analyse"
   - Le rapport sera envoyé automatiquement à <EMAIL>

4. 🔧 CONFIGURATION OLLAMA (optionnel):
   ollama serve                              # Démarrer Ollama
   ollama create cobit-auditeur -f Modelfile # Créer le modèle

5. 📊 FORMATS SUPPORTÉS:
   - PDF avec texte (non scanné)
   - Excel (.xlsx, .xls)
   - Taille maximum: 16MB

6. 🆘 DÉPANNAGE:
   - Vérifiez que Python est installé: python --version
   - Installez les dépendances: pip install flask pandas nltk
   - Vérifiez les logs dans la console

===============================================
""")

def main():
    """Fonction principale."""
    print("🤖 Agent IA d'Audit Gouvernance IT - Démarrage")
    print("=" * 50)
    
    # Vérifier les dépendances
    dependances_manquantes = verifier_dependances_minimales()
    
    if dependances_manquantes:
        logger.info("Installation des dépendances manquantes...")
        if not installer_dependances_manquantes(dependances_manquantes):
            logger.error("❌ Impossible d'installer toutes les dépendances")
            afficher_aide()
            return False
    
    # Configuration
    configurer_nltk()
    creer_dossiers()
    
    # Vérifications optionnelles
    verifier_ollama()
    
    # Démarrage
    logger.info("✅ Configuration terminée")
    return demarrer_application()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n👋 Arrêt de l'application")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Erreur inattendue: {e}")
        afficher_aide()
        sys.exit(1)
