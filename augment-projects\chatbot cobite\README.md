# Chatbot RAG COBIT 2019

Un chatbot **entièrement local** basé sur **Ollama** et **Python** pour répondre aux questions sur **COBIT 2019**.

## 🚀 Fonctionnalités

- ✅ **100% Local** : Aucune donnée envoyée vers des services externes
- ✅ **RAG (Retrieval-Augmented Generation)** : Recherche dans les documents COBIT 2019
- ✅ **API FastAPI** : Interface REST simple et efficace
- ✅ **LlamaIndex** : Moteur de recherche et génération avancé
- ✅ **Ollama** : Modèles LLM locaux (Mistral, Llama3, etc.)

## 📋 Prérequis

### 1. Installer Ollama

Téléchargez et installez Ollama depuis [https://ollama.ai](https://ollama.ai)

### 2. Télécharger les modèles requis

```bash
# Modèle principal pour la génération
ollama pull mistral

# Modèle pour les embeddings
ollama pull nomic-embed-text
```

### 3. Python 3.8+

Assurez-vous d'avoir Python 3.8 ou plus récent installé.

## 🛠️ Installation

### 1. <PERSON><PERSON><PERSON> ou télécharger le projet

```bash
cd "Documents\augment-projects\chatbot cobite"
```

### 2. Créer un environnement virtuel (recommandé)

```bash
python -m venv venv
venv\Scripts\activate  # Sur Windows
# source venv/bin/activate  # Sur Linux/Mac
```

### 3. Installer les dépendances

```bash
pip install -r requirements.txt
```

## 🚀 Utilisation

### 1. Démarrer Ollama

Assurez-vous qu'Ollama est en cours d'exécution :

```bash
ollama serve
```

### 2. Lancer le chatbot

```bash
python main.py
```

Le serveur démarrera sur `http://localhost:8000`

### 3. Tester l'API

#### Via curl :

```bash
# Test de santé
curl http://localhost:8000/health

# Poser une question
curl -X POST "http://localhost:8000/query" \
     -H "Content-Type: application/json" \
     -d '{"question": "Quels sont les 6 principes de COBIT 2019 ?"}'
```

#### Via l'interface Swagger :

Ouvrez votre navigateur et allez à `http://localhost:8000/docs`

## 📁 Structure du projet

```
chatbot-cobite/
├── main.py                 # Application FastAPI principale
├── requirements.txt        # Dépendances Python
├── README.md              # Ce fichier
├── data/                  # Documents COBIT 2019
│   ├── cobit_2019_introduction.txt
│   ├── cobit_2019_governance_objectives.txt
│   ├── cobit_2019_management_objectives.txt
│   └── cobit_2019_enablers.txt
└── storage/               # Index vectoriel (créé automatiquement)
```

## 🔧 Configuration

### Changer le modèle LLM

Dans `main.py`, modifiez la ligne :

```python
Settings.llm = Ollama(
    model="llama3",  # Changez ici pour llama3, codellama, etc.
    base_url="http://localhost:11434",
    request_timeout=120.0
)
```

### Ajouter des documents

1. Placez vos fichiers `.txt` dans le dossier `data/`
2. Supprimez le dossier `storage/` pour forcer la recréation de l'index
3. Redémarrez l'application

## 📊 Exemples de questions

- "Quels sont les 6 principes de COBIT 2019 ?"
- "Expliquez l'objectif EDM01"
- "Quelle est la différence entre gouvernance et gestion dans COBIT ?"
- "Quels sont les domaines de gestion dans COBIT 2019 ?"
- "Décrivez les 7 enablers de COBIT"
- "Comment COBIT 2019 gère-t-il les risques ?"

## 🐛 Dépannage

### Erreur de connexion Ollama

```
Erreur: Impossible de se connecter à Ollama
```

**Solution :**
1. Vérifiez qu'Ollama est démarré : `ollama serve`
2. Vérifiez que les modèles sont téléchargés : `ollama list`

### Erreur de modèle manquant

```
Erreur: Model 'mistral' not found
```

**Solution :**
```bash
ollama pull mistral
ollama pull nomic-embed-text
```

### Erreur de mémoire

```
Erreur: Out of memory
```

**Solution :**
- Utilisez un modèle plus petit (ex: `llama3:8b` au lieu de `llama3:70b`)
- Fermez d'autres applications gourmandes en mémoire

### Pas de documents trouvés

```
Erreur: Aucun fichier .txt trouvé dans data/
```

**Solution :**
- Vérifiez que les fichiers `.txt` sont bien dans le dossier `data/`
- Vérifiez les permissions de lecture des fichiers

## 🔒 Sécurité et Confidentialité

- ✅ **Aucune donnée externe** : Tout fonctionne localement
- ✅ **Pas de télémétrie** : Aucune donnée envoyée vers des serveurs tiers
- ✅ **Contrôle total** : Vous maîtrisez vos données et modèles

## 🚀 Améliorations possibles

- [ ] Interface web avec Streamlit ou Gradio
- [ ] Support de fichiers PDF et Word
- [ ] Système de cache pour les réponses
- [ ] Métriques de performance
- [ ] Support multilingue
- [ ] Intégration avec d'autres frameworks (ITIL, ISO 27001, etc.)

## 📝 Licence

Ce projet est fourni à des fins éducatives et de démonstration.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
- Ajouter plus de contenu COBIT 2019
- Améliorer les prompts
- Optimiser les performances
- Ajouter des fonctionnalités

## 📞 Support

En cas de problème :
1. Vérifiez la section dépannage ci-dessus
2. Consultez les logs de l'application
3. Vérifiez que tous les prérequis sont installés
