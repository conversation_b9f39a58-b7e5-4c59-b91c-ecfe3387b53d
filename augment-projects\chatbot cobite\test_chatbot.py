"""
Script de test pour le chatbot COBIT 2019
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8001"

def test_health():
    """Test de l'endpoint de santé"""
    print("🔍 Test de l'endpoint de santé...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ Endpoint de santé OK")
            print(f"   Réponse: {response.json()}")
            return True
        else:
            print(f"❌ Erreur de santé: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def test_query(question):
    """Test d'une question"""
    print(f"\n🤖 Test de la question: '{question}'")
    try:
        payload = {"question": question}
        response = requests.post(
            f"{BASE_URL}/query",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Question traitée avec succès")
            print(f"   Réponse: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Erreur lors de la question: {response.status_code}")
            print(f"   Détail: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests du chatbot COBIT 2019")
    print("=" * 50)
    
    # Test de santé
    if not test_health():
        print("\n❌ Le serveur n'est pas accessible. Vérifiez qu'il est démarré.")
        return
    
    # Questions de test
    test_questions = [
        "Quels sont les 6 principes de COBIT 2019 ?",
        "Expliquez l'objectif EDM01",
        "Quels sont les domaines de gestion dans COBIT 2019 ?",
        "Décrivez les 7 enablers de COBIT",
        "Quelle est la différence entre gouvernance et gestion ?"
    ]
    
    print(f"\n🧪 Test de {len(test_questions)} questions...")
    
    success_count = 0
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- Test {i}/{len(test_questions)} ---")
        if test_query(question):
            success_count += 1
        time.sleep(2)  # Pause entre les tests
    
    # Résumé
    print("\n" + "=" * 50)
    print(f"📊 Résultats des tests:")
    print(f"   ✅ Réussis: {success_count}/{len(test_questions)}")
    print(f"   ❌ Échoués: {len(test_questions) - success_count}/{len(test_questions)}")
    
    if success_count == len(test_questions):
        print("\n🎉 Tous les tests sont passés avec succès !")
    else:
        print(f"\n⚠️  {len(test_questions) - success_count} test(s) ont échoué.")

if __name__ == "__main__":
    main()
