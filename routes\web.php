<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CobitController;
use App\Http\Controllers\ChatbotController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Routes d'authentification
Route::get('/login', [App\Http\Controllers\AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [App\Http\Controllers\AuthController::class, 'login']);
Route::post('/logout', [App\Http\Controllers\AuthController::class, 'logout'])->name('logout');

// Route principale - Redirection vers la page d'accueil COBIT
Route::get('/', function () {
    return redirect('/cobit/home');
});

// Routes pour l'évaluation COBIT (protégées par authentification)
Route::prefix('cobit')->name('cobit.')->middleware(App\Http\Middleware\AuthMiddleware::class)->group(function () {
    // Page d'accueil KPMG
    Route::get('/home', [CobitController::class, 'home'])->name('home');
    Route::get('/', [CobitController::class, 'home'])->name('index');

    // Pages des Design Factors
    Route::get('/df/{number}', [CobitController::class, 'dfDetail'])->name('df.detail');
    // CRUD Évaluations
    Route::post('/evaluation/create', [CobitController::class, 'createEvaluation'])->name('evaluation.create');
    Route::get('/evaluation/{id}/df/{df}', [CobitController::class, 'showEvaluationDF'])->name('evaluation.df');
    Route::post('/evaluation/save-df', [CobitController::class, 'saveDFData'])->name('evaluation.save-df');
    Route::get('/evaluation/{id}/canvas', [CobitController::class, 'showCanvas'])->name('evaluation.canvas');
    Route::delete('/evaluation/{id}', [CobitController::class, 'deleteEvaluation'])->name('evaluation.delete');

    // Nouvelle évaluation (ancienne méthode - à supprimer plus tard)
    Route::post('/nouvelle-evaluation', [CobitController::class, 'nouvelleEvaluation'])->name('nouvelle.evaluation');

    // Canvas final
    Route::get('/canvas-final', [CobitController::class, 'canvasFinal'])->name('canvas.final');

    // Historique des évaluations
    Route::get('/historique', [CobitController::class, 'historique'])->name('historique');
    Route::get('/historique/canvas/{id}', [CobitController::class, 'viewCanvasFromHistory'])->name('historique.canvas');

    // Routes pour la comparaison d'évaluations
    Route::get('/comparison', [CobitController::class, 'comparisonPage'])->name('comparison');
    Route::post('/comparison/analyze', [CobitController::class, 'analyzeComparison'])->name('comparison.analyze');

    // Anciennes routes (pour compatibilité)
    Route::get('/evaluation', [CobitController::class, 'evaluationTest'])->name('evaluation');
    Route::post('/save-evaluation', [CobitController::class, 'saveEvaluation'])->name('save-evaluation');
    Route::get('/results', [CobitController::class, 'results'])->name('results');
    Route::get('/export-pdf', [CobitController::class, 'exportPdf'])->name('export-pdf');
    Route::post('/import', [CobitController::class, 'import'])->name('import');
    Route::delete('/reset', [CobitController::class, 'reset'])->name('reset');

    // API Routes pour les données en temps réel
    Route::get('/api/calculate/{dfNumber}', [CobitController::class, 'calculateResults'])->name('api.calculate');
    Route::post('/api/update-inputs', [CobitController::class, 'updateInputs'])->name('api.update-inputs');
    Route::post('/api/save-df', [CobitController::class, 'saveDFData'])->name('api.save-df');

    // Routes d'export
    Route::post('/export/pdf', [App\Http\Controllers\ExportController::class, 'exportPDF'])->name('export.pdf');
    Route::post('/export/excel', [App\Http\Controllers\ExportController::class, 'exportExcel'])->name('export.excel');

    // Routes pour le chatbot COBIT (avec authentification)
    Route::prefix('chatbot')->name('chatbot.')->group(function () {
        Route::get('/health', [ChatbotController::class, 'health'])->name('health');
        Route::post('/query', [ChatbotController::class, 'query'])->name('query');
        Route::get('/suggestions', [ChatbotController::class, 'suggestions'])->name('suggestions');
        Route::get('/history', [ChatbotController::class, 'history'])->name('history');
        Route::get('/stats', [ChatbotController::class, 'stats'])->name('stats');
    });

    // Route simple pour lancer l'Agent AI COBIT
    Route::post('/launch-agent-ai', function (Illuminate\Http\Request $request) {
        try {
            $agentPath = $request->input('path', 'C:\Users\<USER>\Desktop\symfcopitejihed\symfcopite\symf\symfcobite\cobit-laravel\Agent ai cobit');

            // Vérifier que le répertoire existe
            if (!is_dir($agentPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Le répertoire Agent AI n\'existe pas: ' . $agentPath
                ]);
            }

            // Vérifier que app_test.py existe (version de test)
            $appFile = $agentPath . DIRECTORY_SEPARATOR . 'app_test.py';
            if (!file_exists($appFile)) {
                // Fallback vers app.py si app_test.py n'existe pas
                $appFile = $agentPath . DIRECTORY_SEPARATOR . 'app.py';
                if (!file_exists($appFile)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Aucun fichier app_test.py ou app.py trouvé dans: ' . $agentPath
                    ]);
                }
                $scriptName = 'app.py';
            } else {
                $scriptName = 'app_test.py';
            }

            // Lancer l'application Flask Agent AI
            if (PHP_OS_FAMILY === 'Windows') {
                // Windows - lancer Flask en arrière-plan
                $command = "cd /d \"{$agentPath}\" && start /B python {$scriptName}";
                exec($command);
            } else {
                // Linux/Mac - lancer Flask en arrière-plan
                $command = "cd \"{$agentPath}\" && python {$scriptName} > /dev/null 2>&1 &";
                exec($command);
            }

            return response()->json([
                'success' => true,
                'message' => 'Agent AI COBIT (Flask) lancé depuis: ' . $agentPath . '\n\nInterface disponible sur: http://localhost:5000',
                'path' => $agentPath,
                'url' => 'http://localhost:5000'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur: ' . $e->getMessage()
            ]);
        }
    })->name('launch.agent.ai');

    // Route pour vérifier si l'Agent AI est en cours d'exécution
    Route::get('/check-agent-ai', function () {
        try {
            // Vérifier si le port 5000 est ouvert (Flask)
            $connection = @fsockopen('localhost', 5000, $errno, $errstr, 1);

            if ($connection) {
                fclose($connection);
                return response()->json([
                    'success' => true,
                    'running' => true,
                    'message' => 'Agent AI COBIT est en cours d\'exécution',
                    'url' => 'http://localhost:5000'
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'running' => false,
                    'message' => 'Agent AI COBIT n\'est pas en cours d\'exécution'
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la vérification: ' . $e->getMessage()
            ]);
        }
    })->name('check.agent.ai');
});
