<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent IA - Audit Gouvernance IT COBIT 2019</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            background: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .email-info {
            background: #e8f4fd;
            border: 1px solid #b3d9f2;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
        }

        .email-info h3 {
            color: #2c5aa0;
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .email-info p {
            color: #5a7ba0;
            margin: 0;
        }

        .submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .features {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .features ul {
            list-style: none;
            color: #666;
        }

        .features li {
            padding: 5px 0;
            padding-left: 25px;
            position: relative;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Agent IA - Audit Gouvernance IT</h1>
            <p>Analyse automatique selon le référentiel COBIT 2019</p>
        </div>

        <form id="uploadForm" action="/upload" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="document">📄 Document à analyser</label>
                <input type="file" id="document" name="document" class="file-input"
                       accept=".pdf,.xlsx,.xls" required>
                <small style="color: #666; margin-top: 5px; display: block;">
                    Formats supportés: PDF, Excel (.xlsx, .xls)
                </small>
            </div>

            <div class="email-info">
                <h3>📧 Envoi automatique du rapport</h3>
                <p>Le rapport d'audit sera automatiquement envoyé à : <strong><EMAIL></strong></p>
            </div>

            <button type="submit" class="submit-btn">
                🚀 Lancer l'analyse et envoyer le rapport
            </button>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Analyse en cours... Cela peut prendre quelques minutes.</p>
            </div>
        </form>

        <div class="features">
            <h3>🎯 Fonctionnalités de l'agent</h3>
            <ul>
                <li>Extraction automatique des mots-clés de gouvernance IT</li>
                <li>Analyse des besoins client selon COBIT 2019</li>
                <li>Évaluation de maturité des processus</li>
                <li>Recommandations stratégiques personnalisées</li>
                <li>Prédictions et feuille de route détaillée</li>
                <li>Rapport professionnel envoyé par email</li>
            </ul>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', function() {
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.submit-btn').style.display = 'none';
        });

        // Améliorer l'affichage du nom de fichier
        document.getElementById('document').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                const label = document.querySelector('label[for="document"]');
                label.innerHTML = `📄 Document sélectionné: <strong>${fileName}</strong>`;
            }
        });
    </script>
</body>
</html>
