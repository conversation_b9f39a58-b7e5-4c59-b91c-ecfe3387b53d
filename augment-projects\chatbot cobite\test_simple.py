"""
Test simple pour vérifier le chatbot
"""

import requests
import json

def test_simple():
    """Test simple d'une question"""
    
    url = "http://localhost:8001/query"
    question = "Qu'est-ce que COBIT ?"
    
    print(f"🤖 Test de la question: {question}")
    
    try:
        response = requests.post(
            url,
            json={"question": question},
            timeout=60
        )
        
        print(f"Status code: {response.status_code}")
        print(f"Headers: {response.headers}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Réponse JSON: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            answer = data.get("answer", "")
            print(f"\nRéponse: {answer}")
            print(f"Longueur: {len(answer)} caractères")
        else:
            print(f"Erreur: {response.status_code}")
            print(f"Contenu: {response.text}")
            
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    test_simple()
