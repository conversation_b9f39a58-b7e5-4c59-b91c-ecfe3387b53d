"""
Test de performance du chatbot COBIT 2019
Teste la vitesse de réponse et la qualité des réponses
"""

import requests
import time
import json
from typing import List, Dict

def test_performance():
    """Test de performance du chatbot"""
    
    base_url = "http://localhost:8001"
    
    # Questions de test avec complexité variable
    questions = [
        {
            "question": "Qu'est-ce que COBIT ?",
            "type": "simple",
            "expected_keywords": ["cobit", "gouvernance", "gestion", "framework"]
        },
        {
            "question": "Quels sont les 6 principes de COBIT 2019 ?",
            "type": "liste",
            "expected_keywords": ["principes", "6", "cobit"]
        },
        {
            "question": "Expliquez en détail l'objectif EDM01 et ses composants",
            "type": "complexe",
            "expected_keywords": ["edm01", "gouvernance", "cadre"]
        },
        {
            "question": "Comment COBIT 2019 différencie-t-il la gouvernance de la gestion ?",
            "type": "complexe",
            "expected_keywords": ["gouvernance", "gestion", "différence"]
        },
        {
            "question": "Décrivez les 7 enablers et leur rôle dans COBIT",
            "type": "complexe",
            "expected_keywords": ["enablers", "7", "rôle"]
        }
    ]
    
    print("🚀 Test de performance du chatbot COBIT 2019")
    print("=" * 60)
    
    # Test de santé
    print("🔍 Vérification de l'état du serveur...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Serveur opérationnel")
            print(f"   📊 Documents chargés: {health_data.get('documents_loaded', 0)}")
            print(f"   🔗 Ollama connecté: {health_data.get('ollama_connected', False)}")
        else:
            print(f"❌ Erreur serveur: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter au serveur: {e}")
        return
    
    print("\n🧪 Tests de performance...")
    print("-" * 60)
    
    results = []
    total_time = 0
    
    for i, test_case in enumerate(questions, 1):
        print(f"\n--- Test {i}/{len(questions)} ({test_case['type']}) ---")
        print(f"❓ Question: {test_case['question']}")
        
        # Mesurer le temps de réponse
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/query",
                json={"question": test_case["question"]},
                timeout=120  # 2 minutes max
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            total_time += response_time
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("response", "")
                
                # Vérifier la présence de mots-clés attendus
                keywords_found = []
                for keyword in test_case["expected_keywords"]:
                    if keyword.lower() in answer.lower():
                        keywords_found.append(keyword)
                
                keyword_score = len(keywords_found) / len(test_case["expected_keywords"])
                
                # Évaluer la longueur de la réponse
                answer_length = len(answer)
                length_score = min(answer_length / 200, 1.0)  # Score basé sur 200 chars minimum
                
                # Score global
                overall_score = (keyword_score * 0.7) + (length_score * 0.3)
                
                print(f"✅ Réponse reçue en {response_time:.2f}s")
                print(f"   📏 Longueur: {answer_length} caractères")
                print(f"   🎯 Mots-clés trouvés: {len(keywords_found)}/{len(test_case['expected_keywords'])}")
                print(f"   📊 Score qualité: {overall_score:.2f}/1.0")
                print(f"   💬 Aperçu: {answer[:100]}...")
                
                results.append({
                    "question": test_case["question"],
                    "type": test_case["type"],
                    "response_time": response_time,
                    "answer_length": answer_length,
                    "keywords_found": keywords_found,
                    "keyword_score": keyword_score,
                    "overall_score": overall_score,
                    "success": True
                })
                
            else:
                print(f"❌ Erreur HTTP: {response.status_code}")
                results.append({
                    "question": test_case["question"],
                    "type": test_case["type"],
                    "response_time": response_time,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout après 120 secondes")
            results.append({
                "question": test_case["question"],
                "type": test_case["type"],
                "success": False,
                "error": "Timeout"
            })
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
            results.append({
                "question": test_case["question"],
                "type": test_case["type"],
                "success": False,
                "error": str(e)
            })
    
    # Résumé des résultats
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES PERFORMANCES")
    print("=" * 60)
    
    successful_tests = [r for r in results if r.get("success", False)]
    failed_tests = [r for r in results if not r.get("success", False)]
    
    print(f"✅ Tests réussis: {len(successful_tests)}/{len(results)}")
    print(f"❌ Tests échoués: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_response_time = sum(r["response_time"] for r in successful_tests) / len(successful_tests)
        avg_quality_score = sum(r["overall_score"] for r in successful_tests) / len(successful_tests)
        avg_length = sum(r["answer_length"] for r in successful_tests) / len(successful_tests)
        
        print(f"\n⏱️  Temps de réponse moyen: {avg_response_time:.2f}s")
        print(f"📏 Longueur moyenne des réponses: {avg_length:.0f} caractères")
        print(f"🎯 Score qualité moyen: {avg_quality_score:.2f}/1.0")
        print(f"⚡ Temps total: {total_time:.2f}s")
        
        # Analyse par type de question
        print(f"\n📈 Analyse par type de question:")
        for question_type in ["simple", "liste", "complexe"]:
            type_results = [r for r in successful_tests if r["type"] == question_type]
            if type_results:
                type_avg_time = sum(r["response_time"] for r in type_results) / len(type_results)
                type_avg_score = sum(r["overall_score"] for r in type_results) / len(type_results)
                print(f"   {question_type.capitalize()}: {type_avg_time:.2f}s, score {type_avg_score:.2f}")
        
        # Recommandations
        print(f"\n💡 Recommandations:")
        if avg_response_time > 10:
            print("   ⚠️  Temps de réponse élevé - considérez un modèle plus léger")
        elif avg_response_time < 3:
            print("   ✅ Temps de réponse excellent")
        else:
            print("   ✅ Temps de réponse acceptable")
            
        if avg_quality_score > 0.8:
            print("   ✅ Qualité des réponses excellente")
        elif avg_quality_score > 0.6:
            print("   ⚠️  Qualité des réponses acceptable")
        else:
            print("   ❌ Qualité des réponses à améliorer")
    
    if failed_tests:
        print(f"\n❌ Tests échoués:")
        for test in failed_tests:
            print(f"   - {test['question'][:50]}... : {test.get('error', 'Erreur inconnue')}")
    
    print(f"\n🎉 Test de performance terminé !")

if __name__ == "__main__":
    test_performance()
