@echo off
echo ========================================
echo   COBIT 2019 RAG Chatbot - Demarrage
echo ========================================
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

REM Verifier si Ollama est installe
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Ollama n'est pas installe ou pas dans le PATH
    echo Veuillez installer Ollama depuis https://ollama.ai
    pause
    exit /b 1
)

echo ✅ Python detecte
echo ✅ Ollama detecte
echo.

REM Verifier si l'environnement virtuel existe
if not exist "venv" (
    echo Creation de l'environnement virtuel...
    python -m venv venv
    if errorlevel 1 (
        echo ERREUR: Impossible de creer l'environnement virtuel
        pause
        exit /b 1
    )
)

REM Activer l'environnement virtuel
echo Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Installer les dependances si necessaire
if not exist "venv\Lib\site-packages\fastapi" (
    echo Installation des dependances...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer les dependances
        pause
        exit /b 1
    )
)

REM Verifier si Ollama est demarre
echo Verification d'Ollama...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  Ollama ne semble pas etre demarre
    echo    Veuillez demarrer Ollama dans un autre terminal avec:
    echo    ollama serve
    echo.
    echo    Puis appuyez sur une touche pour continuer...
    pause >nul
)

REM Verifier si le modele est disponible
echo Verification du modele gemma2:2b...
ollama list | findstr "gemma2:2b" >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  Le modele gemma2:2b n'est pas installe
    echo    Telechargement en cours... (cela peut prendre du temps)
    ollama pull gemma2:2b
    if errorlevel 1 (
        echo ERREUR: Impossible de telecharger le modele
        pause
        exit /b 1
    )
)

echo.
echo 🚀 Demarrage du chatbot COBIT 2019...
echo    Interface web: http://localhost:8001/docs
echo    API: http://localhost:8001
echo.
echo    Appuyez sur Ctrl+C pour arreter
echo.

REM Demarrer l'application
python main.py

echo.
echo Chatbot arrete.
pause
