# Configuration détaillée des objectifs COBIT 2019
# Ce fichier contient la structure complète du référentiel COBIT 2019

OBJECTIFS_COBIT_2019 = {
    "EDM": {
        "nom": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>er et Surveiller",
        "description": "Gouvernance d'entreprise des technologies de l'information",
        "objectifs": {
            "EDM01": {
                "nom": "Assurer la définition et le maintien du cadre de gouvernance",
                "description": "Définir et maintenir un cadre de gouvernance IT aligné sur la stratégie d'entreprise",
                "pratiques": [
                    "Définir les structures de gouvernance",
                    "Établir les rôles et responsabilités",
                    "Maintenir les politiques et procédures"
                ]
            },
            "EDM02": {
                "nom": "Assurer la réalisation des bénéfices",
                "description": "Optimiser la création de valeur des investissements IT",
                "pratiques": [
                    "Évaluer l'optimisation de la valeur",
                    "Diriger l'optimisation de la valeur",
                    "Surveiller l'optimisation de la valeur"
                ]
            },
            "EDM03": {
                "nom": "Assurer l'optimisation des risques",
                "description": "Gérer les risques IT de manière appropriée",
                "pratiques": [
                    "Évaluer la gestion des risques",
                    "Diriger la gestion des risques",
                    "Surveiller la gestion des risques"
                ]
            },
            "EDM04": {
                "nom": "Assurer l'optimisation des ressources",
                "description": "Optimiser l'utilisation des ressources IT",
                "pratiques": [
                    "Évaluer la gestion des ressources",
                    "Diriger la gestion des ressources",
                    "Surveiller la gestion des ressources"
                ]
            },
            "EDM05": {
                "nom": "Assurer la transparence vis-à-vis des parties prenantes",
                "description": "Maintenir la transparence et la communication avec les parties prenantes",
                "pratiques": [
                    "Évaluer les exigences de reporting",
                    "Diriger la communication",
                    "Surveiller la communication"
                ]
            }
        }
    },
    "APO": {
        "nom": "Aligner, Planifier et Organiser",
        "description": "Stratégie et tactiques IT pour contribuer aux objectifs métier",
        "objectifs": {
            "APO01": {
                "nom": "Gérer le cadre de gestion IT",
                "description": "Maintenir et améliorer continuellement le cadre de gestion IT",
                "pratiques": [
                    "Définir le cadre de gestion IT",
                    "Maintenir l'alignement avec la gouvernance",
                    "Surveiller et évaluer le cadre"
                ]
            },
            "APO02": {
                "nom": "Gérer la stratégie",
                "description": "Développer et maintenir la stratégie IT alignée sur la stratégie métier",
                "pratiques": [
                    "Comprendre la direction de l'entreprise",
                    "Évaluer l'état actuel et définir la cible",
                    "Définir le plan stratégique IT"
                ]
            },
            "APO03": {
                "nom": "Gérer l'architecture d'entreprise",
                "description": "Établir une architecture d'entreprise commune",
                "pratiques": [
                    "Développer la vision de l'architecture",
                    "Définir l'architecture de référence",
                    "Sélectionner les opportunités et solutions"
                ]
            },
            "APO04": {
                "nom": "Gérer l'innovation",
                "description": "Maintenir une veille sur l'environnement technologique",
                "pratiques": [
                    "Créer un environnement propice à l'innovation",
                    "Maintenir une compréhension de l'entreprise",
                    "Surveiller et analyser l'environnement technologique"
                ]
            },
            "APO05": {
                "nom": "Gérer le portefeuille",
                "description": "Exécuter la direction stratégique du portefeuille d'investissements",
                "pratiques": [
                    "Établir la structure du portefeuille",
                    "Évaluer et sélectionner les programmes",
                    "Surveiller et rapporter la performance du portefeuille"
                ]
            },
            "APO06": {
                "nom": "Gérer le budget et les coûts",
                "description": "Gérer les activités financières IT et de contrôle des coûts",
                "pratiques": [
                    "Gérer la comptabilité et l'allocation des coûts IT",
                    "Gérer le budget IT",
                    "Gérer les coûts"
                ]
            },
            "APO07": {
                "nom": "Gérer les ressources humaines",
                "description": "Optimiser les capacités des ressources humaines IT",
                "pratiques": [
                    "Maintenir un personnel adéquat",
                    "Identifier les compétences clés",
                    "Gérer le personnel IT"
                ]
            }
        }
    }
}

NIVEAUX_MATURITE = {
    0: {
        "nom": "Inexistant",
        "description": "Absence complète de processus reconnaissables",
        "caracteristiques": [
            "Aucun processus identifiable",
            "Absence de sensibilisation aux problèmes",
            "Pas de standardisation"
        ]
    },
    1: {
        "nom": "Initial/Ad hoc",
        "description": "Processus ad hoc et désorganisés",
        "caracteristiques": [
            "Processus imprévisibles et réactifs",
            "Travail accompli mais souvent retardé",
            "Approche désorganisée"
        ]
    },
    2: {
        "nom": "Répétable mais intuitif",
        "description": "Processus suivent un modèle régulier",
        "caracteristiques": [
            "Processus développés au niveau des projets",
            "Souvent réactifs",
            "Discipline de processus peu probable"
        ]
    },
    3: {
        "nom": "Processus défini",
        "description": "Processus documentés et communiqués",
        "caracteristiques": [
            "Processus standardisés et documentés",
            "Formation obligatoire",
            "Processus améliorés"
        ]
    },
    4: {
        "nom": "Géré et mesurable",
        "description": "Processus surveillés et mesurés",
        "caracteristiques": [
            "Gestion et mesure de la conformité",
            "Automatisation et outils",
            "Amélioration continue"
        ]
    },
    5: {
        "nom": "Optimisé",
        "description": "Bonnes pratiques suivies et automatisées",
        "caracteristiques": [
            "Processus affinés au niveau des bonnes pratiques",
            "IT utilisée de façon intégrée",
            "Amélioration continue institutionnalisée"
        ]
    }
}

DOMAINES_FOCUS = {
    "alignement_strategique": {
        "nom": "Alignement Stratégique",
        "description": "Alignement entre les stratégies métier et IT",
        "objectifs_lies": ["EDM01", "APO01", "APO02", "APO03"]
    },
    "creation_valeur": {
        "nom": "Création de Valeur",
        "description": "Optimisation des investissements et création de valeur",
        "objectifs_lies": ["EDM02", "APO05", "APO06", "BAI01"]
    },
    "gestion_risques": {
        "nom": "Gestion des Risques",
        "description": "Préservation des actifs IT et gestion des risques",
        "objectifs_lies": ["EDM03", "APO12", "DSS05", "MEA02"]
    },
    "gestion_ressources": {
        "nom": "Gestion des Ressources",
        "description": "Optimisation des ressources IT",
        "objectifs_lies": ["EDM04", "APO07", "BAI09", "DSS01"]
    },
    "mesure_performance": {
        "nom": "Mesure de Performance",
        "description": "Transparence et responsabilité de la performance",
        "objectifs_lies": ["EDM05", "MEA01", "MEA03", "MEA04"]
    }
}

INDICATEURS_PERFORMANCE = {
    "KGI": {
        "nom": "Indicateurs Clés d'Objectifs",
        "description": "Mesures qui indiquent si un objectif IT a été atteint",
        "exemples": [
            "Pourcentage de projets IT livrés dans les temps",
            "ROI des investissements IT",
            "Satisfaction des utilisateurs métier"
        ]
    },
    "KPI": {
        "nom": "Indicateurs Clés de Performance",
        "description": "Mesures qui déterminent la performance d'un processus IT",
        "exemples": [
            "Temps de résolution des incidents",
            "Disponibilité des systèmes",
            "Respect du budget IT"
        ]
    }
}
