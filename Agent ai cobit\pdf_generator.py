#!/usr/bin/env python3
"""
Générateur de PDF avancé pour les rapports d'audit COBIT 2019
Avec schémas, tableaux, graphiques et design ergonomique
"""

import os
import io
from datetime import datetime
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.graphics.shapes import Drawing, Rect, String
from reportlab.graphics.charts.piecharts import Pie
from reportlab.graphics.charts.barcharts import VerticalBarChart
from reportlab.graphics.charts.legends import Legend
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY

# Configuration matplotlib pour éviter les problèmes d'affichage
plt.style.use('default')
sns.set_palette("husl")

class RapportPDFGenerator:
    """Générateur de rapports PDF avancés pour l'audit COBIT."""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
        
    def setup_custom_styles(self):
        """Configure les styles personnalisés."""
        # Style pour les titres principaux
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Style pour les sous-titres
        self.styles.add(ParagraphStyle(
            name='CustomHeading2',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#34495e'),
            fontName='Helvetica-Bold'
        ))
        
        # Style pour le texte normal avec couleur
        self.styles.add(ParagraphStyle(
            name='CustomNormal',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            textColor=colors.HexColor('#2c3e50'),
            alignment=TA_JUSTIFY
        ))
        
        # Style pour les highlights
        self.styles.add(ParagraphStyle(
            name='Highlight',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=8,
            textColor=colors.HexColor('#e74c3c'),
            fontName='Helvetica-Bold'
        ))

    def creer_graphique_maturite(self, scores_maturite: Dict[str, float]) -> str:
        """Crée un graphique radar de maturité COBIT."""
        fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
        
        # Données pour le graphique radar
        domaines = list(scores_maturite.keys())
        scores = list(scores_maturite.values())
        
        # Ajouter le premier point à la fin pour fermer le polygone
        domaines_complets = domaines + [domaines[0]]
        scores_complets = scores + [scores[0]]
        
        # Angles pour chaque domaine
        angles = np.linspace(0, 2 * np.pi, len(domaines), endpoint=False).tolist()
        angles += angles[:1]
        
        # Créer le graphique
        ax.plot(angles, scores_complets, 'o-', linewidth=2, label='Niveau actuel', color='#3498db')
        ax.fill(angles, scores_complets, alpha=0.25, color='#3498db')
        
        # Ligne de référence pour le niveau cible (4)
        scores_cible = [4] * len(angles)
        ax.plot(angles, scores_cible, '--', linewidth=2, label='Niveau cible', color='#e74c3c')
        
        # Configuration des axes
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(domaines)
        ax.set_ylim(0, 5)
        ax.set_yticks([1, 2, 3, 4, 5])
        ax.set_yticklabels(['1', '2', '3', '4', '5'])
        ax.grid(True)
        
        # Titre et légende
        plt.title('Évaluation de Maturité COBIT 2019', size=16, fontweight='bold', pad=20)
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        # Sauvegarder
        chemin_image = 'temp_maturite_radar.png'
        plt.savefig(chemin_image, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chemin_image

    def creer_graphique_roi(self, donnees_roi: Dict) -> str:
        """Crée un graphique de ROI et bénéfices."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # Graphique 1: Investissement vs Bénéfices
        categories = ['Investissement\nInitial', 'Bénéfices\nAnnuels']
        valeurs = [donnees_roi.get('investissement_estime', 0), donnees_roi.get('benefices_annuels', 0)]
        couleurs = ['#e74c3c', '#27ae60']
        
        bars1 = ax1.bar(categories, valeurs, color=couleurs, alpha=0.8)
        ax1.set_title('Analyse Financière', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Montant (€)')
        
        # Ajouter les valeurs sur les barres
        for bar, valeur in zip(bars1, valeurs):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{valeur:,.0f}€', ha='center', va='bottom', fontweight='bold')
        
        # Graphique 2: Timeline de ROI
        annees = ['Année 1', 'Année 2', 'Année 3']
        benefices_cumules = [
            donnees_roi.get('benefices_annuels', 0) - donnees_roi.get('investissement_estime', 0),
            donnees_roi.get('benefices_annuels', 0) * 2 - donnees_roi.get('investissement_estime', 0),
            donnees_roi.get('benefices_annuels', 0) * 3 - donnees_roi.get('investissement_estime', 0)
        ]
        
        ax2.plot(annees, benefices_cumules, marker='o', linewidth=3, markersize=8, color='#3498db')
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('Évolution du ROI', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Bénéfices Cumulés (€)')
        ax2.grid(True, alpha=0.3)
        
        # Colorer les zones positives et négatives
        ax2.fill_between(annees, benefices_cumules, 0, 
                        where=[b >= 0 for b in benefices_cumules], 
                        color='green', alpha=0.3, interpolate=True)
        ax2.fill_between(annees, benefices_cumules, 0, 
                        where=[b < 0 for b in benefices_cumules], 
                        color='red', alpha=0.3, interpolate=True)
        
        plt.tight_layout()
        
        # Sauvegarder
        chemin_image = 'temp_roi_analysis.png'
        plt.savefig(chemin_image, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chemin_image

    def creer_tableau_objectifs(self, objectifs_cobit: List[Dict]) -> Table:
        """Crée un tableau stylé des objectifs COBIT."""
        # En-têtes du tableau
        data = [['Code', 'Objectif COBIT', 'Priorité', 'Niveau Actuel', 'Niveau Cible']]
        
        # Données des objectifs
        for obj in objectifs_cobit:
            priorite_color = {
                'Critique': '🔴',
                'Élevée': '🟠', 
                'Moyenne': '🟡',
                'Faible': '🟢'
            }.get(obj.get('priorite', 'Moyenne'), '🟡')
            
            data.append([
                obj.get('code', ''),
                obj.get('nom', '')[:50] + '...' if len(obj.get('nom', '')) > 50 else obj.get('nom', ''),
                f"{priorite_color} {obj.get('priorite', 'Moyenne')}",
                f"{obj.get('niveau_actuel', 0)}/5",
                f"{obj.get('niveau_cible', 0)}/5"
            ])
        
        # Créer le tableau
        table = Table(data, colWidths=[1*cm, 8*cm, 2.5*cm, 2*cm, 2*cm])
        
        # Style du tableau
        table.setStyle(TableStyle([
            # En-tête
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            
            # Corps du tableau
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            
            # Bordures
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),
            
            # Alternance de couleurs
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))
        
        return table

    def generer_pdf_complet(self, donnees_rapport: Dict, nom_fichier: str) -> str:
        """Génère le rapport PDF complet."""
        # Nom du fichier PDF
        nom_pdf = f"rapport_audit_cobit_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        chemin_pdf = os.path.join('reports', nom_pdf)
        
        # Créer le document PDF
        doc = SimpleDocTemplate(chemin_pdf, pagesize=A4, 
                              rightMargin=2*cm, leftMargin=2*cm,
                              topMargin=2*cm, bottomMargin=2*cm)
        
        # Contenu du document
        story = []
        
        # Page de garde
        story.extend(self._creer_page_garde(donnees_rapport, nom_fichier))
        story.append(PageBreak())
        
        # Synthèse exécutive
        story.extend(self._creer_synthese_executive(donnees_rapport))
        story.append(PageBreak())
        
        # Graphiques de maturité
        story.extend(self._creer_section_maturite(donnees_rapport))
        story.append(PageBreak())
        
        # Objectifs COBIT
        story.extend(self._creer_section_objectifs(donnees_rapport))
        story.append(PageBreak())
        
        # Analyse ROI
        story.extend(self._creer_section_roi(donnees_rapport))
        story.append(PageBreak())
        
        # Recommandations
        story.extend(self._creer_section_recommandations(donnees_rapport))
        
        # Construire le PDF
        doc.build(story)
        
        # Nettoyer les fichiers temporaires
        self._nettoyer_fichiers_temp()
        
        return chemin_pdf

    def _creer_page_garde(self, donnees: Dict, nom_fichier: str) -> List:
        """Crée la page de garde du rapport."""
        elements = []

        # Titre principal
        elements.append(Spacer(1, 2*cm))
        elements.append(Paragraph("🤖 RAPPORT D'AUDIT", self.styles['CustomTitle']))
        elements.append(Paragraph("GOUVERNANCE IT - COBIT 2019", self.styles['CustomTitle']))
        elements.append(Spacer(1, 1*cm))

        # Logo ou icône (simulé avec du texte stylé)
        elements.append(Paragraph("🏢 📊 🔍", self.styles['CustomTitle']))
        elements.append(Spacer(1, 2*cm))

        # Informations du rapport
        info_data = [
            ['📄 Fichier analysé:', nom_fichier],
            ['📅 Date de génération:', datetime.now().strftime("%d/%m/%Y à %H:%M")],
            ['🎯 Référentiel:', 'COBIT 2019'],
            ['🤖 Générateur:', 'Agent IA d\'Audit Gouvernance IT'],
            ['📧 Destinataire:', '<EMAIL>']
        ]

        info_table = Table(info_data, colWidths=[4*cm, 8*cm])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('SPACEAFTER', (0, 0), (-1, -1), 6),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.HexColor('#2c3e50')),
            ('TEXTCOLOR', (1, 0), (1, -1), colors.HexColor('#34495e')),
        ]))

        elements.append(info_table)
        elements.append(Spacer(1, 2*cm))

        # Badge de qualité
        elements.append(Paragraph("✅ ANALYSE COMPLÈTE", self.styles['Highlight']))
        elements.append(Paragraph("Rapport généré automatiquement selon les standards COBIT 2019",
                                self.styles['CustomNormal']))

        return elements

    def _creer_synthese_executive(self, donnees: Dict) -> List:
        """Crée la section synthèse exécutive."""
        elements = []

        elements.append(Paragraph("📋 SYNTHÈSE EXÉCUTIVE", self.styles['CustomTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Score global
        score_global = donnees.get('score_maturite', 2.5)
        couleur_score = '#e74c3c' if score_global < 2 else '#f39c12' if score_global < 3.5 else '#27ae60'

        elements.append(Paragraph(f"🎯 <b>Score de Maturité Global: {score_global:.1f}/5</b>",
                                self.styles['Highlight']))

        # Statut
        statut = "Critique" if score_global < 2 else "À améliorer" if score_global < 3.5 else "Satisfaisant"
        elements.append(Paragraph(f"📊 <b>Statut:</b> {statut}", self.styles['CustomNormal']))
        elements.append(Spacer(1, 0.5*cm))

        # Résumé des enjeux
        enjeux = [
            "• Amélioration de la gouvernance IT nécessaire",
            "• Standardisation des processus selon COBIT 2019",
            "• Renforcement de la gestion des risques",
            "• Optimisation des investissements IT",
            "• Amélioration de l'alignement stratégique"
        ]

        elements.append(Paragraph("🔍 <b>Principaux Enjeux Identifiés:</b>", self.styles['CustomHeading2']))
        for enjeu in enjeux:
            elements.append(Paragraph(enjeu, self.styles['CustomNormal']))

        elements.append(Spacer(1, 0.5*cm))

        # Priorités d'action
        elements.append(Paragraph("⚡ <b>Top 3 des Priorités d'Action:</b>", self.styles['CustomHeading2']))
        priorites = [
            "1. 🔴 Définir le cadre de gouvernance IT (EDM01)",
            "2. 🟠 Améliorer la gestion stratégique (APO02)",
            "3. 🟡 Renforcer la gestion des risques (EDM03)"
        ]

        for priorite in priorites:
            elements.append(Paragraph(priorite, self.styles['CustomNormal']))

        return elements

    def _creer_section_maturite(self, donnees: Dict) -> List:
        """Crée la section d'évaluation de maturité avec graphiques."""
        elements = []

        elements.append(Paragraph("📊 ÉVALUATION DE MATURITÉ COBIT", self.styles['CustomTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Données de maturité simulées
        scores_maturite = {
            'EDM\n(Gouvernance)': 2.5,
            'APO\n(Stratégie)': 2.0,
            'BAI\n(Construction)': 3.0,
            'DSS\n(Services)': 2.8,
            'MEA\n(Surveillance)': 2.2
        }

        # Créer et insérer le graphique radar
        chemin_radar = self.creer_graphique_maturite(scores_maturite)
        if os.path.exists(chemin_radar):
            img = Image(chemin_radar, width=12*cm, height=9*cm)
            elements.append(img)
            elements.append(Spacer(1, 0.5*cm))

        # Tableau de détail des niveaux
        elements.append(Paragraph("📋 <b>Détail des Niveaux de Maturité:</b>", self.styles['CustomHeading2']))

        niveaux_data = [
            ['Domaine', 'Niveau Actuel', 'Niveau Cible', 'Écart', 'Effort Requis'],
            ['EDM (Gouvernance)', '2.5/5', '4.0/5', '1.5', '🔴 Élevé'],
            ['APO (Stratégie)', '2.0/5', '4.0/5', '2.0', '🔴 Critique'],
            ['BAI (Construction)', '3.0/5', '4.0/5', '1.0', '🟡 Moyen'],
            ['DSS (Services)', '2.8/5', '4.0/5', '1.2', '🟠 Élevé'],
            ['MEA (Surveillance)', '2.2/5', '4.0/5', '1.8', '🔴 Élevé']
        ]

        niveaux_table = Table(niveaux_data, colWidths=[3.5*cm, 2.5*cm, 2.5*cm, 1.5*cm, 2.5*cm])
        niveaux_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))

        elements.append(niveaux_table)

        return elements

    def _creer_section_objectifs(self, donnees: Dict) -> List:
        """Crée la section des objectifs COBIT."""
        elements = []

        elements.append(Paragraph("🏗️ OBJECTIFS COBIT 2019 RECOMMANDÉS", self.styles['CustomTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Objectifs simulés
        objectifs_cobit = [
            {'code': 'EDM01', 'nom': 'Assurer la définition du cadre de gouvernance', 'priorite': 'Critique', 'niveau_actuel': 2, 'niveau_cible': 4},
            {'code': 'EDM02', 'nom': 'Assurer la réalisation des bénéfices', 'priorite': 'Élevée', 'niveau_actuel': 2, 'niveau_cible': 4},
            {'code': 'APO01', 'nom': 'Gérer le cadre de gestion IT', 'priorite': 'Élevée', 'niveau_actuel': 2, 'niveau_cible': 4},
            {'code': 'APO02', 'nom': 'Gérer la stratégie', 'priorite': 'Critique', 'niveau_actuel': 2, 'niveau_cible': 4},
            {'code': 'DSS05', 'nom': 'Gérer les services de sécurité', 'priorite': 'Élevée', 'niveau_actuel': 3, 'niveau_cible': 4}
        ]

        # Créer le tableau des objectifs
        tableau_objectifs = self.creer_tableau_objectifs(objectifs_cobit)
        elements.append(tableau_objectifs)
        elements.append(Spacer(1, 0.5*cm))

        # Explication des priorités
        elements.append(Paragraph("🎯 <b>Légende des Priorités:</b>", self.styles['CustomHeading2']))
        priorites_info = [
            "🔴 <b>Critique:</b> Action immédiate requise (0-3 mois)",
            "🟠 <b>Élevée:</b> Action prioritaire (3-6 mois)",
            "🟡 <b>Moyenne:</b> Action planifiée (6-12 mois)",
            "🟢 <b>Faible:</b> Action différée (12+ mois)"
        ]

        for info in priorites_info:
            elements.append(Paragraph(info, self.styles['CustomNormal']))

        return elements

    def _creer_section_roi(self, donnees: Dict) -> List:
        """Crée la section d'analyse ROI."""
        elements = []

        elements.append(Paragraph("📈 ANALYSE ROI ET BÉNÉFICES", self.styles['CustomTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Données ROI simulées
        donnees_roi = {
            'investissement_estime': 150000,
            'benefices_annuels': 225000,
            'roi_pourcentage': 150,
            'temps_retour': 0.7
        }

        # Créer et insérer le graphique ROI
        chemin_roi = self.creer_graphique_roi(donnees_roi)
        if os.path.exists(chemin_roi):
            img = Image(chemin_roi, width=14*cm, height=6*cm)
            elements.append(img)
            elements.append(Spacer(1, 0.5*cm))

        # Tableau récapitulatif ROI
        roi_data = [
            ['Métrique', 'Valeur', 'Description'],
            ['💰 Investissement Initial', f"{donnees_roi['investissement_estime']:,}€", 'Coût total de mise en œuvre'],
            ['📊 Bénéfices Annuels', f"{donnees_roi['benefices_annuels']:,}€", 'Gains annuels estimés'],
            ['📈 ROI', f"{donnees_roi['roi_pourcentage']}%", 'Retour sur investissement'],
            ['⏱️ Temps de Retour', f"{donnees_roi['temps_retour']:.1f} ans", 'Période de récupération']
        ]

        roi_table = Table(roi_data, colWidths=[4*cm, 3*cm, 6*cm])
        roi_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#27ae60')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgreen]),
        ]))

        elements.append(roi_table)

        return elements

    def _creer_section_recommandations(self, donnees: Dict) -> List:
        """Crée la section des recommandations."""
        elements = []

        elements.append(Paragraph("💡 RECOMMANDATIONS STRATÉGIQUES", self.styles['CustomTitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Recommandations par horizon
        horizons = [
            {
                'titre': '🚀 Court Terme (0-6 mois)',
                'couleur': '#e74c3c',
                'actions': [
                    'Définir les rôles et responsabilités IT',
                    'Mettre en place des politiques de base',
                    'Former les équipes aux bonnes pratiques COBIT',
                    'Audit des processus existants'
                ]
            },
            {
                'titre': '🏗️ Moyen Terme (6-18 mois)',
                'couleur': '#f39c12',
                'actions': [
                    'Implémenter les processus COBIT prioritaires',
                    'Développer les indicateurs de performance',
                    'Renforcer la gouvernance des données',
                    'Mettre en place des contrôles internes'
                ]
            },
            {
                'titre': '🎯 Long Terme (18+ mois)',
                'couleur': '#27ae60',
                'actions': [
                    'Optimiser et automatiser les processus',
                    'Mettre en place l\'amélioration continue',
                    'Développer l\'innovation IT',
                    'Certification et audit externe'
                ]
            }
        ]

        for horizon in horizons:
            elements.append(Paragraph(horizon['titre'], self.styles['CustomHeading2']))
            for action in horizon['actions']:
                elements.append(Paragraph(f"• {action}", self.styles['CustomNormal']))
            elements.append(Spacer(1, 0.3*cm))

        # Conclusion
        elements.append(Spacer(1, 0.5*cm))
        elements.append(Paragraph("🎯 <b>CONCLUSION</b>", self.styles['CustomHeading2']))
        elements.append(Paragraph(
            "Ce rapport identifie les axes d'amélioration prioritaires pour renforcer la gouvernance IT selon le référentiel COBIT 2019. "
            "La mise en œuvre des recommandations permettra d'améliorer significativement la maturité des processus IT et de créer de la valeur pour l'organisation.",
            self.styles['CustomNormal']
        ))

        return elements

    def _nettoyer_fichiers_temp(self):
        """Nettoie les fichiers temporaires."""
        fichiers_temp = ['temp_maturite_radar.png', 'temp_roi_analysis.png']
        for fichier in fichiers_temp:
            if os.path.exists(fichier):
                try:
                    os.remove(fichier)
                except:
                    pass  # Ignorer les erreurs de suppression
