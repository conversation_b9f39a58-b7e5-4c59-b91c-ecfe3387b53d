# Guide d'Installation Complet - Chatbot RAG COBIT 2019

Ce guide vous accompagne étape par étape pour installer et configurer votre chatbot RAG COBIT 2019 entièrement local.

## 🎯 Vue d'ensemble

Vous allez créer un chatbot qui :
- ✅ Fonctionne **100% en local** (aucune donnée envoyée vers l'extérieur)
- ✅ Répond aux questions sur **COBIT 2019**
- ✅ Utilise **Ollama** pour les modèles LLM
- ✅ Propose une **API REST** avec FastAPI
- ✅ Recherche dans vos documents avec **RAG**

## 📋 Prérequis

### 1. Système d'exploitation
- Windows 10/11, macOS, ou Linux
- Au moins 8 GB de RAM (16 GB recommandé)
- 10 GB d'espace disque libre

### 2. Python
- Python 3.8 ou plus récent
- pip (gestionnaire de packages Python)

Vérifiez votre version :
```bash
python --version
pip --version
```

## 🚀 Installation Étape par Étape

### Étape 1 : Installer Ollama

#### Sur Windows :
1. Allez sur [https://ollama.ai](https://ollama.ai)
2. Téléchargez l'installateur Windows
3. Exécutez l'installateur et suivez les instructions
4. Redémarrez votre terminal

#### Sur macOS :
```bash
# Avec Homebrew
brew install ollama

# Ou téléchargez depuis le site officiel
```

#### Sur Linux :
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

#### Vérification :
```bash
ollama --version
```

### Étape 2 : Télécharger les modèles Ollama

Ouvrez un terminal et exécutez :

```bash
# Démarrer le service Ollama
ollama serve

# Dans un nouveau terminal, télécharger le modèle (choisissez un seul) :

# Option 1 : Modèle léger et rapide (recommandé pour débuter)
ollama pull gemma2:2b

# Option 2 : Modèle plus performant mais plus lourd
ollama pull mistral

# Option 3 : Modèle très performant mais très lourd
ollama pull llama3
```

**Recommandation :** Commencez avec `gemma2:2b` (1.6 GB) pour tester, puis passez à `mistral` (4.1 GB) si vous voulez de meilleures performances.

### Étape 3 : Préparer le projet

1. **Créer le dossier du projet :**
```bash
mkdir chatbot-cobit-2019
cd chatbot-cobit-2019
```

2. **Créer un environnement virtuel Python (recommandé) :**
```bash
python -m venv venv

# Activer l'environnement virtuel :
# Sur Windows :
venv\Scripts\activate
# Sur macOS/Linux :
source venv/bin/activate
```

3. **Créer le fichier requirements.txt :**
```bash
# Créez un fichier requirements.txt avec ce contenu :
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
requests>=2.31.0
python-multipart>=0.0.6
pydantic>=2.5.0
```

4. **Installer les dépendances :**
```bash
pip install -r requirements.txt
```

### Étape 4 : Créer les fichiers du projet

1. **Créer le dossier pour les données :**
```bash
mkdir data
```

2. **Télécharger les fichiers du projet :**
   - `main.py` (application principale)
   - `test_chatbot.py` (script de test)
   - `README.md` (documentation)
   - Fichiers de données COBIT dans le dossier `data/`

### Étape 5 : Configuration

1. **Vérifier que Ollama fonctionne :**
```bash
# Dans un terminal, démarrez Ollama :
ollama serve

# Dans un autre terminal, testez :
ollama list
# Vous devriez voir vos modèles téléchargés
```

2. **Adapter le modèle dans main.py :**
Si vous avez téléchargé un modèle différent de `gemma2:2b`, modifiez la ligne dans `main.py` :
```python
"model": "votre-modele-ici",  # ex: "mistral" ou "llama3"
```

## 🎮 Lancement et Test

### 1. Démarrer le chatbot

```bash
# Assurez-vous qu'Ollama est démarré :
ollama serve

# Dans un autre terminal, dans le dossier du projet :
python main.py
```

Vous devriez voir :
```
🚀 Démarrage du serveur COBIT 2019 Chatbot...
INFO:     Started server process [xxxxx]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001
```

### 2. Tester l'API

#### Test de santé :
```bash
# Dans un nouveau terminal :
curl http://localhost:8001/health
```

#### Test avec une question :
```bash
python test_chatbot.py
```

#### Interface web :
Ouvrez votre navigateur et allez à : `http://localhost:8001/docs`

## 🔧 Dépannage

### Problème : "Ollama not found"
**Solution :**
1. Vérifiez qu'Ollama est installé : `ollama --version`
2. Redémarrez votre terminal
3. Sur Windows, vérifiez que Ollama est dans le PATH

### Problème : "Model not found"
**Solution :**
1. Vérifiez les modèles installés : `ollama list`
2. Téléchargez le modèle : `ollama pull gemma2:2b`
3. Vérifiez le nom du modèle dans `main.py`

### Problème : "Port 8001 already in use"
**Solution :**
1. Changez le port dans `main.py` (ligne avec `port=8001`)
2. Ou arrêtez le processus qui utilise le port

### Problème : "No documents found"
**Solution :**
1. Vérifiez que le dossier `data/` existe
2. Vérifiez qu'il contient des fichiers `.txt`
3. Vérifiez les permissions de lecture

### Problème : Réponses lentes
**Solutions :**
1. Utilisez un modèle plus petit : `gemma2:2b`
2. Fermez d'autres applications gourmandes
3. Augmentez la RAM disponible

### Problème : Réponses de mauvaise qualité
**Solutions :**
1. Utilisez un modèle plus performant : `mistral` ou `llama3`
2. Ajoutez plus de contenu COBIT dans le dossier `data/`
3. Améliorez les prompts dans `main.py`

## 📊 Optimisation des Performances

### Choix du modèle selon votre matériel :

| RAM | Modèle recommandé | Taille | Performance |
|-----|------------------|--------|-------------|
| 8 GB | gemma2:2b | 1.6 GB | Correcte |
| 16 GB | mistral | 4.1 GB | Bonne |
| 32 GB+ | llama3 | 7+ GB | Excellente |

### Conseils d'optimisation :
1. **Fermez les applications inutiles** pendant l'utilisation
2. **Utilisez un SSD** pour de meilleures performances
3. **Ajustez les paramètres** dans `main.py` :
   - `temperature` : 0.1-0.9 (créativité)
   - `max_tokens` : 500-2000 (longueur des réponses)

## 🔒 Sécurité et Confidentialité

✅ **Avantages de cette solution :**
- Aucune donnée envoyée vers des serveurs externes
- Contrôle total sur vos données COBIT
- Pas de limite d'utilisation
- Pas de coût récurrent

⚠️ **Bonnes pratiques :**
- Gardez vos modèles à jour
- Sauvegardez vos données personnalisées
- Utilisez un pare-feu si nécessaire

## 📞 Support

Si vous rencontrez des problèmes :
1. Consultez la section dépannage ci-dessus
2. Vérifiez les logs de l'application
3. Testez avec un modèle plus simple
4. Vérifiez la documentation d'Ollama

## 🎉 Félicitations !

Votre chatbot COBIT 2019 est maintenant opérationnel ! Vous pouvez :
- Poser des questions sur COBIT 2019
- Ajouter vos propres documents
- Personnaliser les réponses
- Intégrer l'API dans d'autres applications
