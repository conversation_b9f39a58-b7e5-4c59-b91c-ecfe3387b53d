"""
Script de démonstration du chatbot COBIT 2019
Lance le serveur et ouvre l'interface web
"""

import requests
import time
import webbrowser
import subprocess
import sys
from pathlib import Path

def check_ollama():
    """Vérifier qu'Ollama est accessible"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_server():
    """Vérifier que le serveur FastAPI est accessible"""
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def demo_questions():
    """Poser quelques questions de démonstration"""
    questions = [
        "Qu'est-ce que COBIT ?",
        "Quels sont les objectifs de gouvernance EDM ?",
        "Expliquez l'objectif APO01"
    ]
    
    print("\n🤖 Démonstration du chatbot COBIT 2019")
    print("=" * 50)
    
    for i, question in enumerate(questions, 1):
        print(f"\n--- Question {i} ---")
        print(f"❓ {question}")
        
        try:
            start_time = time.time()
            response = requests.post(
                "http://localhost:8001/query",
                json={"question": question},
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("response", "")
                
                print(f"✅ Réponse reçue en {end_time - start_time:.1f}s")
                print(f"📝 {answer[:200]}...")
                if len(answer) > 200:
                    print(f"   (Réponse complète: {len(answer)} caractères)")
            else:
                print(f"❌ Erreur: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
        
        if i < len(questions):
            print("\n⏳ Attente avant la prochaine question...")
            time.sleep(2)

def main():
    """Fonction principale de démonstration"""
    
    print("🚀 Démonstration du Chatbot COBIT 2019")
    print("=" * 60)
    
    # Vérifier Ollama
    print("🔍 Vérification d'Ollama...")
    if not check_ollama():
        print("❌ Ollama n'est pas accessible sur localhost:11434")
        print("   Veuillez démarrer Ollama avec: ollama serve")
        return
    print("✅ Ollama est accessible")
    
    # Vérifier le serveur
    print("🔍 Vérification du serveur FastAPI...")
    if not check_server():
        print("❌ Le serveur FastAPI n'est pas accessible sur localhost:8001")
        print("   Veuillez démarrer le serveur avec: python main.py")
        
        # Proposer de démarrer le serveur
        response = input("\n🤔 Voulez-vous que je démarre le serveur ? (o/n): ")
        if response.lower() in ['o', 'oui', 'y', 'yes']:
            print("🚀 Démarrage du serveur...")
            # Note: En production, vous pourriez vouloir démarrer le serveur ici
            print("   Veuillez exécuter 'python main.py' dans un autre terminal")
            print("   Puis relancer ce script")
        return
    
    print("✅ Serveur FastAPI accessible")
    
    # Obtenir les informations du serveur
    try:
        response = requests.get("http://localhost:8001/health")
        health_data = response.json()
        print(f"📊 Documents chargés: {health_data.get('documents_loaded', 0)}")
        print(f"🔗 Ollama connecté: {health_data.get('ollama_connected', False)}")
        print(f"🤖 Modèle: {health_data.get('model', 'inconnu')}")
    except:
        pass
    
    print("\n🌐 Ouverture de l'interface web...")
    
    # Ouvrir l'interface web
    try:
        webbrowser.open("http://localhost:8001/docs")
        print("✅ Interface web ouverte dans votre navigateur")
        print("   URL: http://localhost:8001/docs")
    except Exception as e:
        print(f"⚠️  Impossible d'ouvrir automatiquement le navigateur: {e}")
        print("   Veuillez ouvrir manuellement: http://localhost:8001/docs")
    
    # Proposer une démonstration
    print("\n" + "=" * 60)
    response = input("🤔 Voulez-vous voir une démonstration avec des questions ? (o/n): ")
    
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        demo_questions()
    
    print("\n🎉 Démonstration terminée !")
    print("\n📚 Ressources disponibles :")
    print("   • Interface web: http://localhost:8001/docs")
    print("   • API health: http://localhost:8001/health")
    print("   • Tests: python test_chatbot.py")
    print("   • Performance: python test_performance.py")
    print("   • Guide: GUIDE_INSTALLATION.md")
    print("   • Résultats: RESULTATS_TESTS.md")

if __name__ == "__main__":
    main()
