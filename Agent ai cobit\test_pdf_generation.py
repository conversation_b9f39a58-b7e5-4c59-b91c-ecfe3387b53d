#!/usr/bin/env python3
"""
Test de génération de PDF pour vérifier que tout fonctionne
"""

import os
import sys
from pdf_generator import RapportPDFGenerator

def tester_generation_pdf():
    """Teste la génération de PDF."""
    print("🧪 TEST DE GÉNÉRATION DE PDF")
    print("=" * 40)
    
    try:
        # Créer le générateur PDF
        pdf_generator = RapportPDFGenerator()
        print("✅ Générateur PDF initialisé")
        
        # Données de test
        donnees_test = {
            'score_maturite': 2.5,
            'contenu_original': """
            Notre entreprise souhaite améliorer sa gouvernance IT selon COBIT 2019.
            Nous avons identifié des besoins en matière de gestion des risques,
            d'optimisation des processus et de renforcement de la sécurité.
            """,
            'nom_fichier': 'document_test.pdf'
        }
        
        # Générer le PDF
        print("📊 Génération du rapport PDF...")
        chemin_pdf = pdf_generator.generer_pdf_complet(donnees_test, 'document_test.pdf')
        
        if os.path.exists(chemin_pdf):
            taille = os.path.getsize(chemin_pdf)
            print(f"✅ PDF généré avec succès: {chemin_pdf}")
            print(f"📏 Taille du fichier: {taille:,} bytes")
            
            # Ouvrir le PDF pour vérification
            print(f"📂 Fichier disponible dans: {os.path.abspath(chemin_pdf)}")
            return True
        else:
            print("❌ Échec de la génération PDF")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def tester_graphiques():
    """Teste la génération de graphiques."""
    print("\n🎨 TEST DE GÉNÉRATION DE GRAPHIQUES")
    print("=" * 40)
    
    try:
        pdf_generator = RapportPDFGenerator()
        
        # Test graphique de maturité
        scores_test = {
            'EDM': 2.5,
            'APO': 2.0,
            'BAI': 3.0,
            'DSS': 2.8,
            'MEA': 2.2
        }
        
        print("📊 Génération du graphique de maturité...")
        chemin_radar = pdf_generator.creer_graphique_maturite(scores_test)
        
        if os.path.exists(chemin_radar):
            print(f"✅ Graphique radar créé: {chemin_radar}")
        else:
            print("❌ Échec création graphique radar")
            
        # Test graphique ROI
        donnees_roi = {
            'investissement_estime': 150000,
            'benefices_annuels': 225000,
            'roi_pourcentage': 150,
            'temps_retour': 0.7
        }
        
        print("💰 Génération du graphique ROI...")
        chemin_roi = pdf_generator.creer_graphique_roi(donnees_roi)
        
        if os.path.exists(chemin_roi):
            print(f"✅ Graphique ROI créé: {chemin_roi}")
            return True
        else:
            print("❌ Échec création graphique ROI")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test graphiques: {e}")
        return False

def main():
    """Fonction principale de test."""
    print("🚀 TESTS DE L'AGENT IA AMÉLIORÉ")
    print("=" * 50)
    
    # Test des dépendances
    print("📦 Vérification des dépendances...")
    try:
        import matplotlib
        print("✅ matplotlib disponible")
        import seaborn
        print("✅ seaborn disponible")
        import reportlab
        print("✅ reportlab disponible")
        from PIL import Image
        print("✅ Pillow disponible")
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("💡 Installez avec: pip install matplotlib seaborn reportlab Pillow")
        return False
    
    # Tests de génération
    success_graphiques = tester_graphiques()
    success_pdf = tester_generation_pdf()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DES TESTS:")
    print(f"🎨 Graphiques: {'✅ OK' if success_graphiques else '❌ ÉCHEC'}")
    print(f"📊 PDF: {'✅ OK' if success_pdf else '❌ ÉCHEC'}")
    
    if success_graphiques and success_pdf:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("L'agent peut maintenant générer des rapports PDF avec graphiques.")
        print("\n📋 PROCHAINES ÉTAPES:")
        print("1. Testez l'upload d'un fichier sur http://localhost:5000")
        print("2. Vérifiez la réception de l'email avec PDF")
        print("3. Consultez le rapport PDF généré")
        return True
    else:
        print("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Vérifiez les erreurs ci-dessus et corrigez-les.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
