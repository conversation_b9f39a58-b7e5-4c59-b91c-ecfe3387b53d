import smtplib
import logging
from email.message import EmailMessage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
import os

# Configuration du logging
logger = logging.getLogger(__name__)

# Configuration email
EMAIL_EXPEDITEUR = '<EMAIL>'
MOT_DE_PASSE = 'pyps knxd obnt tisg'  
SERVEUR_SMTP = 'smtp.gmail.com'
PORT_SMTP = 587

def creer_email_html(rapport: str, nom_fichier: str) -> str:
    """Crée un email HTML professionnel pour le rapport."""
    date_rapport = datetime.now().strftime("%d/%m/%Y à %H:%M")

    html_template = f"""
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                font-family: 'Se<PERSON>e UI', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 30px;
            }}
            .header h1 {{
                margin: 0;
                font-size: 2.2em;
            }}
            .header p {{
                margin: 10px 0 0 0;
                font-size: 1.1em;
                opacity: 0.9;
            }}
            .info-box {{
                background: #f8f9fa;
                border-left: 4px solid #667eea;
                padding: 20px;
                margin: 20px 0;
                border-radius: 5px;
            }}
            .rapport-content {{
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 25px;
                margin: 20px 0;
                white-space: pre-wrap;
                font-family: 'Courier New', monospace;
                font-size: 0.9em;
                line-height: 1.5;
            }}
            .footer {{
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin-top: 30px;
                border-top: 3px solid #667eea;
            }}
            .footer p {{
                margin: 5px 0;
                color: #666;
            }}
            .badge {{
                background: #28a745;
                color: white;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 0.8em;
                font-weight: bold;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 Agent IA - Rapport d'Audit</h1>
            <p>Analyse de Gouvernance IT selon COBIT 2019</p>
        </div>

        <div class="info-box">
            <h3>📋 Informations du rapport</h3>
            <p><strong>📄 Fichier analysé:</strong> {nom_fichier}</p>
            <p><strong>📅 Date de génération:</strong> {date_rapport}</p>
            <p><strong>🎯 Référentiel:</strong> COBIT 2019</p>
            <p><strong>🤖 Générateur:</strong> Agent IA d'Audit Gouvernance IT</p>
            <span class="badge">✅ Analyse Complète</span>
        </div>

        <div class="rapport-content">
{rapport}
        </div>

        <div class="footer">
            <p><strong>🏢 Agent IA d'Audit Gouvernance IT</strong></p>
            <p>Rapport généré automatiquement le {date_rapport}</p>
            <p>📧 Pour toute question, contactez l'équipe d'audit IT</p>
        </div>
    </body>
    </html>
    """
    return html_template

def envoyer_email(destinataire: str, rapport: str, nom_fichier: str = "document") -> bool:
    """
    Envoie le rapport d'audit par email avec un format professionnel.

    Args:
        destinataire: Adresse email du destinataire
        rapport: Contenu du rapport d'audit
        nom_fichier: Nom du fichier analysé

    Returns:
        bool: True si l'envoi a réussi, False sinon
    """
    try:
        # Création du message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f'🧠 Rapport d\'Audit Gouvernance IT - {nom_fichier} - {datetime.now().strftime("%d/%m/%Y")}'
        msg['From'] = EMAIL_EXPEDITEUR
        msg['To'] = destinataire

        # Version texte simple
        texte_simple = f"""
RAPPORT D'AUDIT GOUVERNANCE IT - COBIT 2019
===========================================

Fichier analysé: {nom_fichier}
Date: {datetime.now().strftime("%d/%m/%Y à %H:%M")}
Générateur: Agent IA d'Audit Gouvernance IT

{rapport}

---
Rapport généré automatiquement par l'Agent IA d'Audit Gouvernance IT
        """

        # Version HTML
        html_content = creer_email_html(rapport, nom_fichier)

        # Attacher les deux versions
        part_texte = MIMEText(texte_simple, 'plain', 'utf-8')
        part_html = MIMEText(html_content, 'html', 'utf-8')

        msg.attach(part_texte)
        msg.attach(part_html)

        # Envoi de l'email
        logger.info(f"Tentative d'envoi d'email à {destinataire}")

        with smtplib.SMTP(SERVEUR_SMTP, PORT_SMTP) as server:
            server.starttls()  # Activer le chiffrement TLS
            server.login(EMAIL_EXPEDITEUR, MOT_DE_PASSE)
            server.send_message(msg)

        logger.info(f"Email envoyé avec succès à {destinataire}")
        return True

    except smtplib.SMTPAuthenticationError:
        logger.error("Erreur d'authentification SMTP. Vérifiez les identifiants.")
        return False
    except smtplib.SMTPRecipientsRefused:
        logger.error(f"Adresse email refusée: {destinataire}")
        return False
    except smtplib.SMTPServerDisconnected:
        logger.error("Connexion au serveur SMTP interrompue")
        return False
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
        return False

def tester_configuration_email() -> bool:
    """
    Teste la configuration email en envoyant un email de test.

    Returns:
        bool: True si le test réussit, False sinon
    """
    try:
        with smtplib.SMTP(SERVEUR_SMTP, PORT_SMTP) as server:
            server.starttls()
            server.login(EMAIL_EXPEDITEUR, MOT_DE_PASSE)
        logger.info("Configuration email testée avec succès")
        return True
    except Exception as e:
        logger.error(f"Erreur de configuration email: {str(e)}")
        return False
