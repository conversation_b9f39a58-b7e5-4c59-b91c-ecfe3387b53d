<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OllamaCobitService
{
    private $client;
    private $baseUrl;
    private $model;
    private $isAvailable;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 60,
            'connect_timeout' => 10
        ]);
        $this->baseUrl = 'http://localhost:11434';
        $this->model = 'cobit-auditeur';
        $this->isAvailable = $this->checkOllamaAvailability();
    }

    /**
     * Vérifier si Ollama est disponible
     */
    private function checkOllamaAvailability()
    {
        try {
            $response = $this->client->get($this->baseUrl . '/api/tags');
            return $response->getStatusCode() === 200;
        } catch (RequestException $e) {
            Log::warning('Ollama non disponible: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Analyser un document avec Ollama pour COBIT
     */
    public function analyzeDocumentForCobit($content, $documentType = 'unknown')
    {
        if (!$this->isAvailable) {
            return $this->getFallbackAnalysis($content);
        }

        try {
            $prompt = $this->buildCOBITAnalysisPrompt($content, $documentType);
            $response = $this->callOllama($prompt);
            
            if ($response) {
                return $this->parseCOBITResponse($response);
            }
        } catch (\Exception $e) {
            Log::error('Erreur Ollama COBIT: ' . $e->getMessage());
        }

        return $this->getFallbackAnalysis($content);
    }

    /**
     * Construire le prompt spécialisé COBIT 2019
     */
    private function buildCOBITAnalysisPrompt($content, $documentType)
    {
        return "Tu es un expert en gouvernance IT et COBIT 2019. Analyse ce document et fournis une évaluation précise pour les 10 Design Factors.

DOCUMENT À ANALYSER ({$documentType}):
{$content}

INSTRUCTIONS:
1. Analyse le contenu selon le référentiel COBIT 2019
2. Évalue chaque Design Factor sur une échelle de 1-5
3. Justifie tes scores avec des éléments du document
4. Estime le niveau de maturité global

DESIGN FACTORS À ÉVALUER:
- DF1: Enterprise Strategy (Stratégie d'entreprise)
- DF2: Enterprise Goals (Objectifs d'entreprise) 
- DF3: Risk Profile (Profil de risque)
- DF4: I&T-Related Issues (Questions liées à l'I&T)
- DF5: Threat Landscape (Paysage des menaces)
- DF6: Compliance Requirements (Exigences de conformité)
- DF7: Role of IT (Rôle de l'IT)
- DF8: Sourcing Model (Modèle d'approvisionnement)
- DF9: IT Implementation Methods (Méthodes d'implémentation IT)
- DF10: Enterprise Size (Taille de l'entreprise)

RÉPONSE ATTENDUE (FORMAT JSON):
{
  \"df_scores\": {
    \"DF1\": {\"score\": 3, \"confidence\": 0.8, \"justification\": \"...\"},
    \"DF2\": {\"score\": 4, \"confidence\": 0.9, \"justification\": \"...\"},
    ...
  },
  \"maturity_level\": 3.2,
  \"confidence_global\": 0.85,
  \"key_findings\": [\"...\", \"...\"],
  \"recommendations\": [\"...\", \"...\"]
}

Analyse maintenant:";
    }

    /**
     * Appeler Ollama avec le prompt
     */
    private function callOllama($prompt)
    {
        try {
            $response = $this->client->post($this->baseUrl . '/api/generate', [
                'json' => [
                    'model' => $this->model,
                    'prompt' => $prompt,
                    'stream' => false,
                    'options' => [
                        'temperature' => 0.3, // Plus déterministe pour la précision
                        'top_p' => 0.9,
                        'num_predict' => 2000
                    ]
                ]
            ]);

            $body = json_decode($response->getBody()->getContents(), true);
            return $body['response'] ?? null;

        } catch (RequestException $e) {
            Log::error('Erreur appel Ollama: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Parser la réponse Ollama pour COBIT
     */
    private function parseCOBITResponse($response)
    {
        // Essayer d'extraire le JSON de la réponse
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonStr, true);
            
            if ($parsed && isset($parsed['df_scores'])) {
                return $this->validateAndEnhanceAnalysis($parsed);
            }
        }

        // Si le parsing JSON échoue, analyser le texte
        return $this->parseTextResponse($response);
    }

    /**
     * Valider et améliorer l'analyse Ollama
     */
    private function validateAndEnhanceAnalysis($analysis)
    {
        $enhanced = [
            'df_values' => [],
            'maturity_level' => $analysis['maturity_level'] ?? 3.0,
            'confidence' => $analysis['confidence_global'] ?? 0.8,
            'key_findings' => $analysis['key_findings'] ?? [],
            'recommendations' => $analysis['recommendations'] ?? [],
            'ollama_enhanced' => true
        ];

        // Convertir les scores DF en valeurs pour les 40 objectifs
        foreach ($analysis['df_scores'] as $df => $data) {
            $score = max(1, min(5, $data['score']));
            $enhanced['df_values'][$df] = $this->generateObjectiveValues($score, $data['confidence'] ?? 0.8);
        }

        return $enhanced;
    }

    /**
     * Générer les valeurs pour les 40 objectifs basées sur le score DF
     */
    private function generateObjectiveValues($baseScore, $confidence)
    {
        $values = [];
        $variation = (1 - $confidence) * 2; // Plus la confiance est faible, plus la variation est grande
        
        for ($i = 0; $i < 40; $i++) {
            // Variation intelligente basée sur la confiance
            $randomVariation = (rand(-100, 100) / 100) * $variation;
            $value = $baseScore + $randomVariation;
            $values[] = max(1, min(5, round($value, 1)));
        }
        
        return $values;
    }

    /**
     * Parser une réponse texte si JSON échoue
     */
    private function parseTextResponse($response)
    {
        $dfScores = [];
        $maturityLevel = 3.0;
        
        // Rechercher des patterns de scores dans le texte
        for ($i = 1; $i <= 10; $i++) {
            $pattern = "/DF{$i}.*?(\d+(?:\.\d+)?)/i";
            if (preg_match($pattern, $response, $matches)) {
                $score = floatval($matches[1]);
                $dfScores["DF{$i}"] = $this->generateObjectiveValues($score, 0.7);
            } else {
                // Score par défaut basé sur l'analyse textuelle
                $dfScores["DF{$i}"] = $this->generateObjectiveValues(3, 0.6);
            }
        }

        // Rechercher le niveau de maturité
        if (preg_match('/maturité.*?(\d+(?:\.\d+)?)/i', $response, $matches)) {
            $maturityLevel = floatval($matches[1]);
        }

        return [
            'df_values' => $dfScores,
            'maturity_level' => $maturityLevel,
            'confidence' => 0.7,
            'key_findings' => $this->extractKeyFindings($response),
            'recommendations' => $this->extractRecommendations($response),
            'ollama_enhanced' => true
        ];
    }

    /**
     * Extraire les points clés du texte
     */
    private function extractKeyFindings($text)
    {
        $findings = [];
        
        // Rechercher des patterns de points clés
        $patterns = [
            '/(?:point clé|finding|constat).*?[:\-]\s*(.+?)(?:\n|\.)/i',
            '/(?:important|critique|essentiel).*?[:\-]\s*(.+?)(?:\n|\.)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $findings = array_merge($findings, $matches[1]);
            }
        }
        
        return array_slice(array_unique($findings), 0, 5);
    }

    /**
     * Extraire les recommandations du texte
     */
    private function extractRecommendations($text)
    {
        $recommendations = [];
        
        $patterns = [
            '/(?:recommandation|conseil|suggestion).*?[:\-]\s*(.+?)(?:\n|\.)/i',
            '/(?:devrait|doit|il faut).*?(.+?)(?:\n|\.)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $recommendations = array_merge($recommendations, $matches[1]);
            }
        }
        
        return array_slice(array_unique($recommendations), 0, 5);
    }

    /**
     * Analyse de fallback si Ollama n'est pas disponible
     */
    private function getFallbackAnalysis($content)
    {
        Log::info('Utilisation de l\'analyse de fallback (Ollama indisponible)');
        
        // Utiliser l'analyse basique existante mais améliorée
        $contentLower = strtolower($content);
        
        $dfKeywords = [
            'DF1' => ['stratégie', 'objectifs', 'vision', 'mission', 'planification', 'strategic'],
            'DF2' => ['gouvernance', 'direction', 'supervision', 'conseil', 'comité', 'governance'],
            'DF3' => ['risque', 'sécurité', 'menace', 'vulnérabilité', 'incident', 'risk'],
            'DF4' => ['ressources', 'budget', 'financement', 'investissement', 'coût', 'resources'],
            'DF5' => ['parties prenantes', 'client', 'utilisateur', 'fournisseur', 'stakeholder'],
            'DF6' => ['compétences', 'formation', 'expertise', 'qualification', 'skills'],
            'DF7' => ['processus', 'procédure', 'workflow', 'méthode', 'pratique', 'process'],
            'DF8' => ['technologie', 'infrastructure', 'système', 'application', 'technology'],
            'DF9' => ['taille', 'complexité', 'envergure', 'échelle', 'dimension', 'size'],
            'DF10' => ['conformité', 'réglementation', 'audit', 'contrôle', 'norme', 'compliance']
        ];

        $dfValues = [];
        $totalScore = 0;
        
        foreach ($dfKeywords as $df => $keywords) {
            $score = 0;
            foreach ($keywords as $keyword) {
                $score += substr_count($contentLower, $keyword);
            }
            
            // Convertir en score 1-5 avec plus de nuance
            $normalizedScore = min(5, max(1, 1 + ($score * 0.3)));
            $dfValues[$df] = $this->generateObjectiveValues($normalizedScore, 0.6);
            $totalScore += $normalizedScore;
        }

        return [
            'df_values' => $dfValues,
            'maturity_level' => round($totalScore / 10, 1),
            'confidence' => 0.6,
            'key_findings' => ['Analyse basée sur la reconnaissance de mots-clés'],
            'recommendations' => ['Recommandations génériques COBIT 2019'],
            'ollama_enhanced' => false
        ];
    }

    /**
     * Vérifier si Ollama est disponible
     */
    public function isAvailable()
    {
        return $this->isAvailable;
    }

    /**
     * Obtenir des informations sur le modèle
     */
    public function getModelInfo()
    {
        return [
            'model' => $this->model,
            'available' => $this->isAvailable,
            'base_url' => $this->baseUrl
        ];
    }
}
